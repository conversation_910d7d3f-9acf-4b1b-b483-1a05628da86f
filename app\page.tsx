"use client";
import Lenis from "lenis";
import { AnimatePresence } from "motion/react";
import { useEffect, useState } from "react";
import Preloader from "@/components/custom/preloader";
import AboutHome from "@/features/home/<USER>";
import HomeAbout from "@/features/home/<USER>";
import HomeHero from "@/features/home/<USER>";

export default function Home() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const lenis = new Lenis();

    function raf(time: number) {
      lenis.raf(time);
      requestAnimationFrame(raf);
    }

    setTimeout(() => {
      setIsLoading(false);
      document.body.style.cursor = "default";
      window.scrollTo(0, 0);
    }, 2000);

    requestAnimationFrame(raf);
  }, []);
  return (
    <>
      <AnimatePresence mode="wait">
        {isLoading && <Preloader />}
      </AnimatePresence>
      <HomeHero />
      <HomeAbout />
    </>
  );
}
