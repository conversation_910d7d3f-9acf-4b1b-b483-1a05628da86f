"use client";
import { motion, useScroll, useTransform } from "motion/react";
import { useRef } from "react";
import ImagesHero from "./images";
import ScrollParagraphHero from "./paragraphs";
export default function HomeHero() {
  const container = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: container,
    offset: ["start start", "end start"],
  });

  const y = useTransform(scrollYProgress, [0, 1], ["0vh", "120vh"]);
  return (
    <main className="bg-white relative flex h-screen overflow-hidden">
      <motion.div
        style={{ y }}
        className="relative h-full w-full"
        ref={container}
      >
        <div className="flex justify-between z-10 opacity-100 px-4 lg:px-6 py-5 ">
          <p className="text-2xl text-white font-edu tracking-tighter font-semibold">
            Rwanda Connect
          </p>
        </div>
        <ImagesHero />
        <ScrollParagraphHero />
      </motion.div>
    </main>
  );
}
