{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/lenis%401.3.9_react%4019.1.0/node_modules/lenis/package.json", "file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/lenis%401.3.9_react%4019.1.0/node_modules/lenis/packages/core/src/maths.ts", "file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/lenis%401.3.9_react%4019.1.0/node_modules/lenis/packages/core/src/animate.ts", "file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/lenis%401.3.9_react%4019.1.0/node_modules/lenis/packages/core/src/debounce.ts", "file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/lenis%401.3.9_react%4019.1.0/node_modules/lenis/packages/core/src/dimensions.ts", "file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/lenis%401.3.9_react%4019.1.0/node_modules/lenis/packages/core/src/emitter.ts", "file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/lenis%401.3.9_react%4019.1.0/node_modules/lenis/packages/core/src/virtual-scroll.ts", "file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/lenis%401.3.9_react%4019.1.0/node_modules/lenis/packages/core/src/lenis.ts"], "sourcesContent": ["{\n  \"name\": \"lenis\",\n  \"version\": \"1.3.9\",\n  \"description\": \"How smooth scroll should be\",\n  \"type\": \"module\",\n  \"sideEffects\": false,\n  \"author\": \"darkroom.engineering\",\n  \"license\": \"MIT\",\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"git+https://github.com/darkroomengineering/lenis.git\"\n  },\n  \"bugs\": {\n    \"url\": \"https://github.com/darkroomengineering/lenis/issues\"\n  },\n  \"homepage\": \"https://github.com/darkroomengineering/lenis\",\n  \"funding\": {\n    \"type\": \"github\",\n    \"url\": \"https://github.com/sponsors/darkroomengineering\"\n  },\n  \"keywords\": [\n    \"scroll\",\n    \"smooth\",\n    \"lenis\",\n    \"react\",\n    \"vue\"\n  ],\n  \"scripts\": {\n    \"build\": \"pnpm build:core && pnpm build:all\",\n    \"build:core\": \"tsup --config tsup.core.ts\",\n    \"build:all\": \"tsup\",\n    \"dev\": \"pnpm run -w --parallel /^dev:.*/\",\n    \"dev:build\": \"tsup --watch\",\n    \"dev:playground\": \"pnpm --filter playground dev\",\n    \"dev:nuxt\": \"pnpm --filter playground-nuxt dev\",\n    \"readme\": \"node ./scripts/update-readme.js\",\n    \"version:dev\": \"npm version prerelease --preid dev --force --no-git-tag-version\",\n    \"version:patch\": \"npm version patch --force --no-git-tag-version\",\n    \"version:minor\": \"npm version minor --force --no-git-tag-version\",\n    \"version:major\": \"npm version major --force --no-git-tag-version\",\n    \"postversion\": \"pnpm build && pnpm readme\",\n    \"publish:dev\": \"npm publish --tag dev\",\n    \"publish:main\": \"npm publish\"\n  },\n  \"files\": [\n    \"dist\"\n  ],\n  \"devDependencies\": {\n    \"terser\": \"^5.37.0\",\n    \"tsup\": \"^8.3.5\",\n    \"typescript\": \"^5.7.3\"\n  },\n  \"peerDependencies\": {\n    \"react\": \">=17.0.0\",\n    \"vue\": \">=3.0.0\",\n    \"@nuxt/kit\": \">=3.0.0\"\n  },\n  \"peerDependenciesMeta\": {\n    \"react\": {\n      \"optional\": true\n    },\n    \"vue\": {\n      \"optional\": true\n    },\n    \"@nuxt/kit\": {\n      \"optional\": true\n    }\n  },\n  \"unpkg\": \"./dist/lenis.mjs\",\n  \"main\": \"./dist/lenis.mjs\",\n  \"module\": \"./dist/lenis.mjs\",\n  \"types\": \"./dist/lenis.d.ts\",\n  \"exports\": {\n    \".\": {\n      \"types\": \"./dist/lenis.d.ts\",\n      \"default\": \"./dist/lenis.mjs\"\n    },\n    \"./react\": {\n      \"types\": \"./dist/lenis-react.d.ts\",\n      \"default\": \"./dist/lenis-react.mjs\"\n    },\n    \"./snap\": {\n      \"types\": \"./dist/lenis-snap.d.ts\",\n      \"default\": \"./dist/lenis-snap.mjs\"\n    },\n    \"./vue\": {\n      \"types\": \"./dist/lenis-vue.d.ts\",\n      \"default\": \"./dist/lenis-vue.mjs\"\n    },\n    \"./nuxt\": {\n      \"default\": \"./dist/lenis-vue-nuxt.mjs\"\n    },\n    \"./nuxt/runtime/*\": {\n      \"default\": \"./dist/nuxt/runtime/*.mjs\"\n    },\n    \"./dist/*\": \"./dist/*\"\n  }\n}\n", "/**\r\n * Clamp a value between a minimum and maximum value\r\n *\r\n * @param min Minimum value\r\n * @param input Value to clamp\r\n * @param max Maximum value\r\n * @returns Clamped value\r\n */\r\nexport function clamp(min: number, input: number, max: number) {\r\n  return Math.max(min, Math.min(input, max))\r\n}\r\n\r\n/**\r\n * Truncate a floating-point number to a specified number of decimal places\r\n *\r\n * @param value Value to truncate\r\n * @param decimals Number of decimal places to truncate to\r\n * @returns Truncated value\r\n */\r\nexport function truncate(value: number, decimals = 0) {\r\n  return parseFloat(value.toFixed(decimals))\r\n}\r\n\r\n/**\r\n *  Linearly interpolate between two values using an amount (0 <= t <= 1)\r\n *\r\n * @param x First value\r\n * @param y Second value\r\n * @param t Amount to interpolate (0 <= t <= 1)\r\n * @returns Interpolated value\r\n */\r\nexport function lerp(x: number, y: number, t: number) {\r\n  return (1 - t) * x + t * y\r\n}\r\n\r\n/**\r\n * Damp a value over time using a damping factor\r\n * {@link http://www.rorydriscoll.com/2016/03/07/frame-rate-independent-damping-using-lerp/}\r\n *\r\n * @param x Initial value\r\n * @param y Target value\r\n * @param lambda Damping factor\r\n * @param dt Time elapsed since the last update\r\n * @returns Damped value\r\n */\r\nexport function damp(x: number, y: number, lambda: number, deltaTime: number) {\r\n  return lerp(x, y, 1 - Math.exp(-lambda * deltaTime))\r\n}\r\n\r\n/**\r\n * Calculate the modulo of the dividend and divisor while keeping the result within the same sign as the divisor\r\n * {@link https://anguscroll.com/just/just-modulo}\r\n *\r\n * @param n Dividend\r\n * @param d Divisor\r\n * @returns Modulo\r\n */\r\nexport function modulo(n: number, d: number) {\r\n  return ((n % d) + d) % d\r\n}\r\n", "import { clamp, damp } from './maths'\r\nimport type { EasingFunction, FromToOptions, OnUpdateCallback } from './types'\r\n\r\n/**\r\n * Animate class to handle value animations with lerping or easing\r\n *\r\n * @example\r\n * const animate = new Animate()\r\n * animate.fromTo(0, 100, { duration: 1, easing: (t) => t })\r\n * animate.advance(0.5) // 50\r\n */\r\nexport class Animate {\r\n  isRunning = false\r\n  value = 0\r\n  from = 0\r\n  to = 0\r\n  currentTime = 0\r\n\r\n  // These are instanciated in the fromTo method\r\n  lerp?: number\r\n  duration?: number\r\n  easing?: EasingFunction\r\n  onUpdate?: OnUpdateCallback\r\n\r\n  /**\r\n   * Advance the animation by the given delta time\r\n   *\r\n   * @param deltaTime - The time in seconds to advance the animation\r\n   */\r\n  advance(deltaTime: number) {\r\n    if (!this.isRunning) return\r\n\r\n    let completed = false\r\n\r\n    if (this.duration && this.easing) {\r\n      this.currentTime += deltaTime\r\n      const linearProgress = clamp(0, this.currentTime / this.duration, 1)\r\n\r\n      completed = linearProgress >= 1\r\n      const easedProgress = completed ? 1 : this.easing(linearProgress)\r\n      this.value = this.from + (this.to - this.from) * easedProgress\r\n    } else if (this.lerp) {\r\n      this.value = damp(this.value, this.to, this.lerp * 60, deltaTime)\r\n      if (Math.round(this.value) === this.to) {\r\n        this.value = this.to\r\n        completed = true\r\n      }\r\n    } else {\r\n      // If no easing or lerp, just jump to the end value\r\n      this.value = this.to\r\n      completed = true\r\n    }\r\n\r\n    if (completed) {\r\n      this.stop()\r\n    }\r\n\r\n    // Call the onUpdate callback with the current value and completed status\r\n    this.onUpdate?.(this.value, completed)\r\n  }\r\n\r\n  /** Stop the animation */\r\n  stop() {\r\n    this.isRunning = false\r\n  }\r\n\r\n  /**\r\n   * Set up the animation from a starting value to an ending value\r\n   * with optional parameters for lerping, duration, easing, and onUpdate callback\r\n   *\r\n   * @param from - The starting value\r\n   * @param to - The ending value\r\n   * @param options - Options for the animation\r\n   */\r\n  fromTo(\r\n    from: number,\r\n    to: number,\r\n    { lerp, duration, easing, onStart, onUpdate }: FromToOptions\r\n  ) {\r\n    this.from = this.value = from\r\n    this.to = to\r\n    this.lerp = lerp\r\n    this.duration = duration\r\n    this.easing = easing\r\n    this.currentTime = 0\r\n    this.isRunning = true\r\n\r\n    onStart?.()\r\n    this.onUpdate = onUpdate\r\n  }\r\n}\r\n", "export function debounce<CB extends (...args: any[]) => void>(\r\n  callback: CB,\r\n  delay: number\r\n) {\r\n  let timer: number | undefined\r\n  return function <T>(this: T, ...args: Parameters<typeof callback>) {\r\n    let context = this\r\n    clearTimeout(timer)\r\n    timer = setTimeout(() => {\r\n      timer = undefined\r\n      callback.apply(context, args)\r\n    }, delay)\r\n  }\r\n}\r\n", "import { debounce } from './debounce'\r\n\r\n/**\r\n * Dimensions class to handle the size of the content and wrapper\r\n *\r\n * @example\r\n * const dimensions = new Dimensions(wrapper, content)\r\n * dimensions.on('resize', (e) => {\r\n *   console.log(e.width, e.height)\r\n * })\r\n */\r\nexport class Dimensions {\r\n  width = 0\r\n  height = 0\r\n  scrollHeight = 0\r\n  scrollWidth = 0\r\n\r\n  // These are instanciated in the constructor as they need information from the options\r\n  private debouncedResize?: (...args: unknown[]) => void\r\n  private wrapperResizeObserver?: ResizeObserver\r\n  private contentResizeObserver?: ResizeObserver\r\n\r\n  constructor(\r\n    private wrapper: HTMLElement | Window | Element,\r\n    private content: HTMLElement | Element,\r\n    { autoResize = true, debounce: debounceValue = 250 } = {}\r\n  ) {\r\n    if (autoResize) {\r\n      this.debouncedResize = debounce(this.resize, debounceValue)\r\n\r\n      if (this.wrapper instanceof Window) {\r\n        window.addEventListener('resize', this.debouncedResize, false)\r\n      } else {\r\n        this.wrapperResizeObserver = new ResizeObserver(this.debouncedResize)\r\n        this.wrapperResizeObserver.observe(this.wrapper)\r\n      }\r\n\r\n      this.contentResizeObserver = new ResizeObserver(this.debouncedResize)\r\n      this.contentResizeObserver.observe(this.content)\r\n    }\r\n\r\n    this.resize()\r\n  }\r\n\r\n  destroy() {\r\n    this.wrapperResizeObserver?.disconnect()\r\n    this.contentResizeObserver?.disconnect()\r\n\r\n    if (this.wrapper === window && this.debouncedResize) {\r\n      window.removeEventListener('resize', this.debouncedResize, false)\r\n    }\r\n  }\r\n\r\n  resize = () => {\r\n    this.onWrapperResize()\r\n    this.onContentResize()\r\n  }\r\n\r\n  onWrapperResize = () => {\r\n    if (this.wrapper instanceof Window) {\r\n      this.width = window.innerWidth\r\n      this.height = window.innerHeight\r\n    } else {\r\n      this.width = this.wrapper.clientWidth\r\n      this.height = this.wrapper.clientHeight\r\n    }\r\n  }\r\n\r\n  onContentResize = () => {\r\n    if (this.wrapper instanceof Window) {\r\n      this.scrollHeight = this.content.scrollHeight\r\n      this.scrollWidth = this.content.scrollWidth\r\n    } else {\r\n      this.scrollHeight = this.wrapper.scrollHeight\r\n      this.scrollWidth = this.wrapper.scrollWidth\r\n    }\r\n  }\r\n\r\n  get limit() {\r\n    return {\r\n      x: this.scrollWidth - this.width,\r\n      y: this.scrollHeight - this.height,\r\n    }\r\n  }\r\n}\r\n", "/**\r\n * Emitter class to handle events\r\n * @example\r\n * const emitter = new Emitter()\r\n * emitter.on('event', (data) => {\r\n *   console.log(data)\r\n * })\r\n * emitter.emit('event', 'data')\r\n */\r\nexport class Emitter {\r\n  private events: Record<\r\n    string,\r\n    Array<(...args: unknown[]) => void> | undefined\r\n  > = {}\r\n\r\n  /**\r\n   * Emit an event with the given data\r\n   * @param event Event name\r\n   * @param args Data to pass to the event handlers\r\n   */\r\n  emit(event: string, ...args: unknown[]) {\r\n    let callbacks = this.events[event] || []\r\n    for (let i = 0, length = callbacks.length; i < length; i++) {\r\n      callbacks[i]?.(...args)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Add a callback to the event\r\n   * @param event Event name\r\n   * @param cb Callback function\r\n   * @returns Unsubscribe function\r\n   */\r\n  on<CB extends (...args: any[]) => void>(event: string, cb: CB) {\r\n    // Add the callback to the event's callback list, or create a new list with the callback\r\n    this.events[event]?.push(cb) || (this.events[event] = [cb])\r\n\r\n    // Return an unsubscribe function\r\n    return () => {\r\n      this.events[event] = this.events[event]?.filter((i) => cb !== i)\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove a callback from the event\r\n   * @param event Event name\r\n   * @param callback Callback function\r\n   */\r\n  off<CB extends (...args: any[]) => void>(event: string, callback: CB) {\r\n    this.events[event] = this.events[event]?.filter((i) => callback !== i)\r\n  }\r\n\r\n  /**\r\n   * Remove all event listeners and clean up\r\n   */\r\n  destroy() {\r\n    this.events = {}\r\n  }\r\n}\r\n", "import { Emitter } from './emitter'\r\nimport type { VirtualScrollCallback } from './types'\r\n\r\nconst LINE_HEIGHT = 100 / 6\r\nconst listenerOptions: AddEventListenerOptions = { passive: false }\r\n\r\nexport class VirtualScroll {\r\n  touchStart = {\r\n    x: 0,\r\n    y: 0,\r\n  }\r\n  lastDelta = {\r\n    x: 0,\r\n    y: 0,\r\n  }\r\n  window = {\r\n    width: 0,\r\n    height: 0,\r\n  }\r\n  private emitter = new Emitter()\r\n\r\n  constructor(\r\n    private element: HTMLElement,\r\n    private options = { wheelMultiplier: 1, touchMultiplier: 1 }\r\n  ) {\r\n    window.addEventListener('resize', this.onWindowResize, false)\r\n    this.onWindowResize()\r\n\r\n    this.element.addEventListener('wheel', this.onWheel, listenerOptions)\r\n    this.element.addEventListener(\r\n      'touchstart',\r\n      this.onTouchStart,\r\n      listenerOptions\r\n    )\r\n    this.element.addEventListener(\r\n      'touchmove',\r\n      this.onTouchMove,\r\n      listenerOptions\r\n    )\r\n    this.element.addEventListener('touchend', this.onTouchEnd, listenerOptions)\r\n  }\r\n\r\n  /**\r\n   * Add an event listener for the given event and callback\r\n   *\r\n   * @param event Event name\r\n   * @param callback Callback function\r\n   */\r\n  on(event: string, callback: VirtualScrollCallback) {\r\n    return this.emitter.on(event, callback)\r\n  }\r\n\r\n  /** Remove all event listeners and clean up */\r\n  destroy() {\r\n    this.emitter.destroy()\r\n\r\n    window.removeEventListener('resize', this.onWindowResize, false)\r\n\r\n    this.element.removeEventListener('wheel', this.onWheel, listenerOptions)\r\n    this.element.removeEventListener(\r\n      'touchstart',\r\n      this.onTouchStart,\r\n      listenerOptions\r\n    )\r\n    this.element.removeEventListener(\r\n      'touchmove',\r\n      this.onTouchMove,\r\n      listenerOptions\r\n    )\r\n    this.element.removeEventListener(\r\n      'touchend',\r\n      this.onTouchEnd,\r\n      listenerOptions\r\n    )\r\n  }\r\n\r\n  /**\r\n   * Event handler for 'touchstart' event\r\n   *\r\n   * @param event Touch event\r\n   */\r\n  onTouchStart = (event: TouchEvent) => {\r\n    // @ts-expect-error - event.targetTouches is not defined\r\n    const { clientX, clientY } = event.targetTouches\r\n      ? event.targetTouches[0]\r\n      : event\r\n\r\n    this.touchStart.x = clientX\r\n    this.touchStart.y = clientY\r\n\r\n    this.lastDelta = {\r\n      x: 0,\r\n      y: 0,\r\n    }\r\n\r\n    this.emitter.emit('scroll', {\r\n      deltaX: 0,\r\n      deltaY: 0,\r\n      event,\r\n    })\r\n  }\r\n\r\n  /** Event handler for 'touchmove' event */\r\n  onTouchMove = (event: TouchEvent) => {\r\n    // @ts-expect-error - event.targetTouches is not defined\r\n    const { clientX, clientY } = event.targetTouches\r\n      ? event.targetTouches[0]\r\n      : event\r\n\r\n    const deltaX = -(clientX - this.touchStart.x) * this.options.touchMultiplier\r\n    const deltaY = -(clientY - this.touchStart.y) * this.options.touchMultiplier\r\n\r\n    this.touchStart.x = clientX\r\n    this.touchStart.y = clientY\r\n\r\n    this.lastDelta = {\r\n      x: deltaX,\r\n      y: deltaY,\r\n    }\r\n\r\n    this.emitter.emit('scroll', {\r\n      deltaX,\r\n      deltaY,\r\n      event,\r\n    })\r\n  }\r\n\r\n  onTouchEnd = (event: TouchEvent) => {\r\n    this.emitter.emit('scroll', {\r\n      deltaX: this.lastDelta.x,\r\n      deltaY: this.lastDelta.y,\r\n      event,\r\n    })\r\n  }\r\n\r\n  /** Event handler for 'wheel' event */\r\n  onWheel = (event: WheelEvent) => {\r\n    let { deltaX, deltaY, deltaMode } = event\r\n\r\n    const multiplierX =\r\n      deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.window.width : 1\r\n    const multiplierY =\r\n      deltaMode === 1 ? LINE_HEIGHT : deltaMode === 2 ? this.window.height : 1\r\n\r\n    deltaX *= multiplierX\r\n    deltaY *= multiplierY\r\n\r\n    deltaX *= this.options.wheelMultiplier\r\n    deltaY *= this.options.wheelMultiplier\r\n\r\n    this.emitter.emit('scroll', { deltaX, deltaY, event })\r\n  }\r\n\r\n  onWindowResize = () => {\r\n    this.window = {\r\n      width: window.innerWidth,\r\n      height: window.innerHeight,\r\n    }\r\n  }\r\n}\r\n", "import { version } from '../../../package.json'\nimport { Animate } from './animate'\nimport { Dimensions } from './dimensions'\nimport { Emitter } from './emitter'\nimport { clamp, modulo } from './maths'\nimport type {\n  LenisEvent,\n  LenisOptions,\n  ScrollCallback,\n  Scrolling,\n  ScrollToOptions,\n  UserData,\n  VirtualScrollCallback,\n  VirtualScrollData,\n} from './types'\nimport { VirtualScroll } from './virtual-scroll'\n\n// Technical explanation\n// - listen to 'wheel' events\n// - prevent 'wheel' event to prevent scroll\n// - normalize wheel delta\n// - add delta to targetScroll\n// - animate scroll to targetScroll (smooth context)\n// - if animation is not running, listen to 'scroll' events (native context)\n\ntype OptionalPick<T, F extends keyof T> = Omit<T, F> & Partial<Pick<T, F>>\n\nconst defaultEasing = (t: number) => Math.min(1, 1.001 - Math.pow(2, -10 * t))\n\nexport class Lenis {\n  private _isScrolling: Scrolling = false // true when scroll is animating\n  private _isStopped = false // true if user should not be able to scroll - enable/disable programmatically\n  private _isLocked = false // same as isStopped but enabled/disabled when scroll reaches target\n  private _preventNextNativeScrollEvent = false\n  private _resetVelocityTimeout: ReturnType<typeof setTimeout> | null = null\n  private __rafID: number | null = null\n\n  /**\n   * Whether or not the user is touching the screen\n   */\n  isTouching?: boolean\n  /**\n   * The time in ms since the lenis instance was created\n   */\n  time = 0\n  /**\n   * User data that will be forwarded through the scroll event\n   *\n   * @example\n   * lenis.scrollTo(100, {\n   *   userData: {\n   *     foo: 'bar'\n   *   }\n   * })\n   */\n  userData: UserData = {}\n  /**\n   * The last velocity of the scroll\n   */\n  lastVelocity = 0\n  /**\n   * The current velocity of the scroll\n   */\n  velocity = 0\n  /**\n   * The direction of the scroll\n   */\n  direction: 1 | -1 | 0 = 0\n  /**\n   * The options passed to the lenis instance\n   */\n  options: OptionalPick<\n    Required<LenisOptions>,\n    'duration' | 'easing' | 'prevent' | 'virtualScroll'\n  >\n  /**\n   * The target scroll value\n   */\n  targetScroll: number\n  /**\n   * The animated scroll value\n   */\n  animatedScroll: number\n\n  // These are instanciated here as they don't need information from the options\n  private readonly animate = new Animate()\n  private readonly emitter = new Emitter()\n  // These are instanciated in the constructor as they need information from the options\n  readonly dimensions: Dimensions // This is not private because it's used in the Snap class\n  private readonly virtualScroll: VirtualScroll\n\n  constructor({\n    wrapper = window,\n    content = document.documentElement,\n    eventsTarget = wrapper,\n    smoothWheel = true,\n    syncTouch = false,\n    syncTouchLerp = 0.075,\n    touchInertiaExponent = 1.7,\n    duration, // in seconds\n    easing,\n    lerp = 0.1,\n    infinite = false,\n    orientation = 'vertical', // vertical, horizontal\n    gestureOrientation = 'vertical', // vertical, horizontal, both\n    touchMultiplier = 1,\n    wheelMultiplier = 1,\n    autoResize = true,\n    prevent,\n    virtualScroll,\n    overscroll = true,\n    autoRaf = false,\n    anchors = false,\n    autoToggle = false, // https://caniuse.com/?search=transition-behavior\n    allowNestedScroll = false,\n    __experimental__naiveDimensions = false,\n  }: LenisOptions = {}) {\n    // Set version\n    window.lenisVersion = version\n\n    // Check if wrapper is <html>, fallback to window\n    if (!wrapper || wrapper === document.documentElement) {\n      wrapper = window\n    }\n\n    // flip to easing/time based animation if at least one of them is provided\n    if (typeof duration === 'number' && typeof easing !== 'function') {\n      easing = defaultEasing\n    } else if (typeof easing === 'function' && typeof duration !== 'number') {\n      duration = 1\n    }\n\n    // Setup options\n    this.options = {\n      wrapper,\n      content,\n      eventsTarget,\n      smoothWheel,\n      syncTouch,\n      syncTouchLerp,\n      touchInertiaExponent,\n      duration,\n      easing,\n      lerp,\n      infinite,\n      gestureOrientation,\n      orientation,\n      touchMultiplier,\n      wheelMultiplier,\n      autoResize,\n      prevent,\n      virtualScroll,\n      overscroll,\n      autoRaf,\n      anchors,\n      autoToggle,\n      allowNestedScroll,\n      __experimental__naiveDimensions,\n    }\n\n    // Setup dimensions instance\n    this.dimensions = new Dimensions(wrapper, content, { autoResize })\n\n    // Setup class name\n    this.updateClassName()\n\n    // Set the initial scroll value for all scroll information\n    this.targetScroll = this.animatedScroll = this.actualScroll\n\n    // Add event listeners\n    this.options.wrapper.addEventListener('scroll', this.onNativeScroll, false)\n\n    this.options.wrapper.addEventListener('scrollend', this.onScrollEnd, {\n      capture: true,\n    })\n\n    if (this.options.anchors && this.options.wrapper === window) {\n      this.options.wrapper.addEventListener(\n        'click',\n        this.onClick as EventListener,\n        false\n      )\n    }\n\n    this.options.wrapper.addEventListener(\n      'pointerdown',\n      this.onPointerDown as EventListener,\n      false\n    )\n\n    // Setup virtual scroll instance\n    this.virtualScroll = new VirtualScroll(eventsTarget as HTMLElement, {\n      touchMultiplier,\n      wheelMultiplier,\n    })\n    this.virtualScroll.on('scroll', this.onVirtualScroll)\n\n    if (this.options.autoToggle) {\n      this.rootElement.addEventListener('transitionend', this.onTransitionEnd, {\n        passive: true,\n      })\n    }\n\n    if (this.options.autoRaf) {\n      this.__rafID = requestAnimationFrame(this.raf)\n    }\n  }\n\n  /**\n   * Destroy the lenis instance, remove all event listeners and clean up the class name\n   */\n  destroy() {\n    this.emitter.destroy()\n\n    this.options.wrapper.removeEventListener(\n      'scroll',\n      this.onNativeScroll,\n      false\n    )\n\n    this.options.wrapper.removeEventListener('scrollend', this.onScrollEnd, {\n      capture: true,\n    })\n\n    this.options.wrapper.removeEventListener(\n      'pointerdown',\n      this.onPointerDown as EventListener,\n      false\n    )\n\n    if (this.options.anchors && this.options.wrapper === window) {\n      this.options.wrapper.removeEventListener(\n        'click',\n        this.onClick as EventListener,\n        false\n      )\n    }\n\n    this.virtualScroll.destroy()\n    this.dimensions.destroy()\n\n    this.cleanUpClassName()\n\n    if (this.__rafID) {\n      cancelAnimationFrame(this.__rafID)\n    }\n  }\n\n  /**\n   * Add an event listener for the given event and callback\n   *\n   * @param event Event name\n   * @param callback Callback function\n   * @returns Unsubscribe function\n   */\n  on(event: 'scroll', callback: ScrollCallback): () => void\n  on(event: 'virtual-scroll', callback: VirtualScrollCallback): () => void\n  on(event: LenisEvent, callback: any) {\n    return this.emitter.on(event, callback)\n  }\n\n  /**\n   * Remove an event listener for the given event and callback\n   *\n   * @param event Event name\n   * @param callback Callback function\n   */\n  off(event: 'scroll', callback: ScrollCallback): void\n  off(event: 'virtual-scroll', callback: VirtualScrollCallback): void\n  off(event: LenisEvent, callback: any) {\n    return this.emitter.off(event, callback)\n  }\n\n  private onScrollEnd = (e: Event | CustomEvent) => {\n    if (!(e instanceof CustomEvent)) {\n      if (this.isScrolling === 'smooth' || this.isScrolling === false) {\n        e.stopPropagation()\n      }\n    }\n  }\n\n  private dispatchScrollendEvent = () => {\n    this.options.wrapper.dispatchEvent(\n      new CustomEvent('scrollend', {\n        bubbles: this.options.wrapper === window,\n        // cancelable: false,\n        detail: {\n          lenisScrollEnd: true,\n        },\n      })\n    )\n  }\n\n  private onTransitionEnd = (event: TransitionEvent) => {\n    if (event.propertyName.includes('overflow')) {\n      const property = this.isHorizontal ? 'overflow-x' : 'overflow-y'\n\n      const overflow = getComputedStyle(this.rootElement)[\n        property as keyof CSSStyleDeclaration\n      ] as string\n\n      if (['hidden', 'clip'].includes(overflow)) {\n        this.internalStop()\n      } else {\n        this.internalStart()\n      }\n    }\n  }\n\n  private setScroll(scroll: number) {\n    // behavior: 'instant' bypasses the scroll-behavior CSS property\n\n    if (this.isHorizontal) {\n      this.options.wrapper.scrollTo({ left: scroll, behavior: 'instant' })\n    } else {\n      this.options.wrapper.scrollTo({ top: scroll, behavior: 'instant' })\n    }\n  }\n\n  private onClick = (event: PointerEvent | MouseEvent) => {\n    const path = event.composedPath()\n    const anchor = path.find(\n      (node) =>\n        node instanceof HTMLAnchorElement &&\n        (node.getAttribute('href')?.startsWith('#') ||\n          node.getAttribute('href')?.startsWith('/#') ||\n          node.getAttribute('href')?.startsWith('./#'))\n    ) as HTMLAnchorElement | undefined\n    if (anchor) {\n      const id = anchor.getAttribute('href')\n\n      if (id) {\n        const options =\n          typeof this.options.anchors === 'object' && this.options.anchors\n            ? this.options.anchors\n            : undefined\n\n        let target: number | string = `#${id.split('#')[1]}`\n        if (['#', '/#', './#', '#top', '/#top', './#top'].includes(id)) {\n          target = 0\n        }\n\n        this.scrollTo(target, options)\n      }\n    }\n  }\n\n  private onPointerDown = (event: PointerEvent | MouseEvent) => {\n    if (event.button === 1) {\n      this.reset()\n    }\n  }\n\n  private onVirtualScroll = (data: VirtualScrollData) => {\n    if (\n      typeof this.options.virtualScroll === 'function' &&\n      this.options.virtualScroll(data) === false\n    )\n      return\n\n    const { deltaX, deltaY, event } = data\n\n    this.emitter.emit('virtual-scroll', { deltaX, deltaY, event })\n\n    // keep zoom feature\n    if (event.ctrlKey) return\n    // @ts-ignore\n    if (event.lenisStopPropagation) return\n\n    const isTouch = event.type.includes('touch')\n    const isWheel = event.type.includes('wheel')\n\n    this.isTouching = event.type === 'touchstart' || event.type === 'touchmove'\n    // if (event.type === 'touchend') {\n    //   console.log('touchend', this.scroll)\n    //   // this.lastVelocity = this.velocity\n    //   // this.velocity = 0\n    //   // this.isScrolling = false\n    //   this.emit({ type: 'touchend' })\n    //   // alert('touchend')\n    //   return\n    // }\n\n    const isClickOrTap = deltaX === 0 && deltaY === 0\n\n    const isTapToStop =\n      this.options.syncTouch &&\n      isTouch &&\n      event.type === 'touchstart' &&\n      isClickOrTap &&\n      !this.isStopped &&\n      !this.isLocked\n\n    if (isTapToStop) {\n      this.reset()\n      return\n    }\n\n    // const isPullToRefresh =\n    //   this.options.gestureOrientation === 'vertical' &&\n    //   this.scroll === 0 &&\n    //   !this.options.infinite &&\n    //   deltaY <= 5 // touch pull to refresh, not reliable yet\n\n    const isUnknownGesture =\n      (this.options.gestureOrientation === 'vertical' && deltaY === 0) ||\n      (this.options.gestureOrientation === 'horizontal' && deltaX === 0)\n\n    if (isClickOrTap || isUnknownGesture) {\n      // console.log('prevent')\n      return\n    }\n\n    // catch if scrolling on nested scroll elements\n    let composedPath = event.composedPath()\n    composedPath = composedPath.slice(0, composedPath.indexOf(this.rootElement)) // remove parents elements\n\n    const prevent = this.options.prevent\n\n    if (\n      !!composedPath.find(\n        (node) =>\n          node instanceof HTMLElement &&\n          ((typeof prevent === 'function' && prevent?.(node)) ||\n            node.hasAttribute?.('data-lenis-prevent') ||\n            (isTouch && node.hasAttribute?.('data-lenis-prevent-touch')) ||\n            (isWheel && node.hasAttribute?.('data-lenis-prevent-wheel')) ||\n            (this.options.allowNestedScroll &&\n              this.checkNestedScroll(node, { deltaX, deltaY })))\n      )\n    )\n      return\n\n    if (this.isStopped || this.isLocked) {\n      if (event.cancelable) {\n        event.preventDefault() // this will stop forwarding the event to the parent, this is problematic\n      }\n      return\n    }\n\n    const isSmooth =\n      (this.options.syncTouch && isTouch) ||\n      (this.options.smoothWheel && isWheel)\n\n    if (!isSmooth) {\n      this.isScrolling = 'native'\n      this.animate.stop()\n      // @ts-ignore\n      event.lenisStopPropagation = true\n      return\n    }\n\n    let delta = deltaY\n    if (this.options.gestureOrientation === 'both') {\n      delta = Math.abs(deltaY) > Math.abs(deltaX) ? deltaY : deltaX\n    } else if (this.options.gestureOrientation === 'horizontal') {\n      delta = deltaX\n    }\n\n    if (\n      !this.options.overscroll ||\n      this.options.infinite ||\n      (this.options.wrapper !== window &&\n        ((this.animatedScroll > 0 && this.animatedScroll < this.limit) ||\n          (this.animatedScroll === 0 && deltaY > 0) ||\n          (this.animatedScroll === this.limit && deltaY < 0)))\n    ) {\n      // @ts-ignore\n      event.lenisStopPropagation = true\n      // event.stopPropagation()\n    }\n\n    if (event.cancelable) {\n      event.preventDefault()\n    }\n\n    const isSyncTouch = isTouch && this.options.syncTouch\n    const isTouchEnd = isTouch && event.type === 'touchend'\n\n    const hasTouchInertia = isTouchEnd\n\n    if (hasTouchInertia) {\n      // delta = this.velocity * this.options.touchInertiaMultiplier\n      delta =\n        Math.sign(this.velocity) *\n        Math.pow(Math.abs(this.velocity), this.options.touchInertiaExponent)\n    }\n\n    this.scrollTo(this.targetScroll + delta, {\n      programmatic: false,\n      ...(isSyncTouch\n        ? {\n            lerp: hasTouchInertia ? this.options.syncTouchLerp : 1,\n            // immediate: !hasTouchInertia,\n          }\n        : {\n            lerp: this.options.lerp,\n            duration: this.options.duration,\n            easing: this.options.easing,\n          }),\n    })\n  }\n\n  /**\n   * Force lenis to recalculate the dimensions\n   */\n  resize() {\n    this.dimensions.resize()\n    this.animatedScroll = this.targetScroll = this.actualScroll\n    this.emit()\n  }\n\n  private emit() {\n    this.emitter.emit('scroll', this)\n  }\n\n  private onNativeScroll = () => {\n    if (this._resetVelocityTimeout !== null) {\n      clearTimeout(this._resetVelocityTimeout)\n      this._resetVelocityTimeout = null\n    }\n\n    if (this._preventNextNativeScrollEvent) {\n      this._preventNextNativeScrollEvent = false\n      return\n    }\n\n    if (this.isScrolling === false || this.isScrolling === 'native') {\n      const lastScroll = this.animatedScroll\n      this.animatedScroll = this.targetScroll = this.actualScroll\n      this.lastVelocity = this.velocity\n      this.velocity = this.animatedScroll - lastScroll\n      this.direction = Math.sign(\n        this.animatedScroll - lastScroll\n      ) as Lenis['direction']\n\n      if (!this.isStopped) {\n        this.isScrolling = 'native'\n      }\n\n      this.emit()\n\n      if (this.velocity !== 0) {\n        this._resetVelocityTimeout = setTimeout(() => {\n          this.lastVelocity = this.velocity\n          this.velocity = 0\n          this.isScrolling = false\n          this.emit()\n        }, 400)\n      }\n    }\n  }\n\n  private reset() {\n    this.isLocked = false\n    this.isScrolling = false\n    this.animatedScroll = this.targetScroll = this.actualScroll\n    this.lastVelocity = this.velocity = 0\n    this.animate.stop()\n  }\n\n  /**\n   * Start lenis scroll after it has been stopped\n   */\n  start() {\n    if (!this.isStopped) return\n\n    if (this.options.autoToggle) {\n      this.rootElement.style.removeProperty('overflow')\n      return\n    }\n\n    this.internalStart()\n  }\n\n  private internalStart() {\n    if (!this.isStopped) return\n\n    this.reset()\n    this.isStopped = false\n    this.emit()\n  }\n\n  /**\n   * Stop lenis scroll\n   */\n  stop() {\n    if (this.isStopped) return\n\n    if (this.options.autoToggle) {\n      this.rootElement.style.setProperty('overflow', 'clip')\n      return\n    }\n\n    this.internalStop()\n  }\n\n  private internalStop() {\n    if (this.isStopped) return\n\n    this.reset()\n    this.isStopped = true\n    this.emit()\n  }\n\n  /**\n   * RequestAnimationFrame for lenis\n   *\n   * @param time The time in ms from an external clock like `requestAnimationFrame` or Tempus\n   */\n  raf = (time: number) => {\n    const deltaTime = time - (this.time || time)\n    this.time = time\n\n    this.animate.advance(deltaTime * 0.001)\n\n    if (this.options.autoRaf) {\n      this.__rafID = requestAnimationFrame(this.raf)\n    }\n  }\n\n  /**\n   * Scroll to a target value\n   *\n   * @param target The target value to scroll to\n   * @param options The options for the scroll\n   *\n   * @example\n   * lenis.scrollTo(100, {\n   *   offset: 100,\n   *   duration: 1,\n   *   easing: (t) => 1 - Math.cos((t * Math.PI) / 2),\n   *   lerp: 0.1,\n   *   onStart: () => {\n   *     console.log('onStart')\n   *   },\n   *   onComplete: () => {\n   *     console.log('onComplete')\n   *   },\n   * })\n   */\n  scrollTo(\n    target: number | string | HTMLElement,\n    {\n      offset = 0,\n      immediate = false,\n      lock = false,\n      duration = this.options.duration,\n      easing = this.options.easing,\n      lerp = this.options.lerp,\n      onStart,\n      onComplete,\n      force = false, // scroll even if stopped\n      programmatic = true, // called from outside of the class\n      userData,\n    }: ScrollToOptions = {}\n  ) {\n    if ((this.isStopped || this.isLocked) && !force) return\n\n    // keywords\n    if (\n      typeof target === 'string' &&\n      ['top', 'left', 'start'].includes(target)\n    ) {\n      target = 0\n    } else if (\n      typeof target === 'string' &&\n      ['bottom', 'right', 'end'].includes(target)\n    ) {\n      target = this.limit\n    } else {\n      let node\n\n      if (typeof target === 'string') {\n        // CSS selector\n        node = document.querySelector(target)\n      } else if (target instanceof HTMLElement && target?.nodeType) {\n        // Node element\n        node = target\n      }\n\n      if (node) {\n        if (this.options.wrapper !== window) {\n          // nested scroll offset correction\n          const wrapperRect = this.rootElement.getBoundingClientRect()\n          offset -= this.isHorizontal ? wrapperRect.left : wrapperRect.top\n        }\n\n        const rect = node.getBoundingClientRect()\n\n        target =\n          (this.isHorizontal ? rect.left : rect.top) + this.animatedScroll\n      }\n    }\n\n    if (typeof target !== 'number') return\n\n    target += offset\n    target = Math.round(target)\n\n    if (this.options.infinite) {\n      if (programmatic) {\n        this.targetScroll = this.animatedScroll = this.scroll\n\n        const distance = target - this.animatedScroll\n\n        if (distance > this.limit / 2) {\n          target = target - this.limit\n        } else if (distance < -this.limit / 2) {\n          target = target + this.limit\n        }\n      }\n    } else {\n      target = clamp(0, target, this.limit)\n    }\n\n    if (target === this.targetScroll) {\n      onStart?.(this)\n      onComplete?.(this)\n      return\n    }\n\n    this.userData = userData ?? {}\n\n    if (immediate) {\n      this.animatedScroll = this.targetScroll = target\n      this.setScroll(this.scroll)\n      this.reset()\n      this.preventNextNativeScrollEvent()\n      this.emit()\n      onComplete?.(this)\n      this.userData = {}\n\n      requestAnimationFrame(() => {\n        this.dispatchScrollendEvent()\n      })\n      return\n    }\n\n    if (!programmatic) {\n      this.targetScroll = target\n    }\n\n    // flip to easing/time based animation if at least one of them is provided\n    if (typeof duration === 'number' && typeof easing !== 'function') {\n      easing = defaultEasing\n    } else if (typeof easing === 'function' && typeof duration !== 'number') {\n      duration = 1\n    }\n\n    this.animate.fromTo(this.animatedScroll, target, {\n      duration,\n      easing,\n      lerp,\n      onStart: () => {\n        // started\n        if (lock) this.isLocked = true\n        this.isScrolling = 'smooth'\n        onStart?.(this)\n      },\n      onUpdate: (value: number, completed: boolean) => {\n        this.isScrolling = 'smooth'\n\n        // updated\n        this.lastVelocity = this.velocity\n        this.velocity = value - this.animatedScroll\n        this.direction = Math.sign(this.velocity) as Lenis['direction']\n\n        this.animatedScroll = value\n        this.setScroll(this.scroll)\n\n        if (programmatic) {\n          // wheel during programmatic should stop it\n          this.targetScroll = value\n        }\n\n        if (!completed) this.emit()\n\n        if (completed) {\n          this.reset()\n          this.emit()\n          onComplete?.(this)\n          this.userData = {}\n\n          requestAnimationFrame(() => {\n            this.dispatchScrollendEvent()\n          })\n\n          // avoid emitting event twice\n          this.preventNextNativeScrollEvent()\n        }\n      },\n    })\n  }\n\n  private preventNextNativeScrollEvent() {\n    this._preventNextNativeScrollEvent = true\n\n    requestAnimationFrame(() => {\n      this._preventNextNativeScrollEvent = false\n    })\n  }\n\n  private checkNestedScroll(\n    node: HTMLElement,\n    { deltaX, deltaY }: { deltaX: number; deltaY: number }\n  ) {\n    const time = Date.now()\n\n    // @ts-ignore\n    const cache = (node._lenis ??= {})\n\n    let hasOverflowX,\n      hasOverflowY,\n      isScrollableX,\n      isScrollableY,\n      scrollWidth,\n      scrollHeight,\n      clientWidth,\n      clientHeight\n\n    const gestureOrientation = this.options.gestureOrientation\n\n    if (time - (cache.time ?? 0) > 2000) {\n      cache.time = Date.now()\n\n      const computedStyle = window.getComputedStyle(node)\n      cache.computedStyle = computedStyle\n\n      const overflowXString = computedStyle.overflowX\n      const overflowYString = computedStyle.overflowY\n\n      hasOverflowX = ['auto', 'overlay', 'scroll'].includes(overflowXString)\n      hasOverflowY = ['auto', 'overlay', 'scroll'].includes(overflowYString)\n      cache.hasOverflowX = hasOverflowX\n      cache.hasOverflowY = hasOverflowY\n\n      if (!hasOverflowX && !hasOverflowY) return false // if no overflow, it's not scrollable no matter what, early return saves some computations\n      if (gestureOrientation === 'vertical' && !hasOverflowY) return false\n      if (gestureOrientation === 'horizontal' && !hasOverflowX) return false\n\n      scrollWidth = node.scrollWidth\n      scrollHeight = node.scrollHeight\n\n      clientWidth = node.clientWidth\n      clientHeight = node.clientHeight\n\n      isScrollableX = scrollWidth > clientWidth\n      isScrollableY = scrollHeight > clientHeight\n\n      cache.isScrollableX = isScrollableX\n      cache.isScrollableY = isScrollableY\n      cache.scrollWidth = scrollWidth\n      cache.scrollHeight = scrollHeight\n      cache.clientWidth = clientWidth\n      cache.clientHeight = clientHeight\n    } else {\n      isScrollableX = cache.isScrollableX\n      isScrollableY = cache.isScrollableY\n      hasOverflowX = cache.hasOverflowX\n      hasOverflowY = cache.hasOverflowY\n      scrollWidth = cache.scrollWidth\n      scrollHeight = cache.scrollHeight\n      clientWidth = cache.clientWidth\n      clientHeight = cache.clientHeight\n    }\n\n    if (\n      (!hasOverflowX && !hasOverflowY) ||\n      (!isScrollableX && !isScrollableY)\n    ) {\n      return false\n    }\n\n    if (gestureOrientation === 'vertical' && (!hasOverflowY || !isScrollableY))\n      return false\n\n    if (\n      gestureOrientation === 'horizontal' &&\n      (!hasOverflowX || !isScrollableX)\n    )\n      return false\n\n    let orientation: 'x' | 'y' | undefined\n\n    if (gestureOrientation === 'horizontal') {\n      orientation = 'x'\n    } else if (gestureOrientation === 'vertical') {\n      orientation = 'y'\n    } else {\n      const isScrollingX = deltaX !== 0\n      const isScrollingY = deltaY !== 0\n\n      if (isScrollingX && hasOverflowX && isScrollableX) {\n        orientation = 'x'\n      }\n\n      if (isScrollingY && hasOverflowY && isScrollableY) {\n        orientation = 'y'\n      }\n    }\n\n    if (!orientation) return false\n\n    let scroll, maxScroll, delta, hasOverflow, isScrollable\n\n    if (orientation === 'x') {\n      scroll = node.scrollLeft\n      maxScroll = scrollWidth - clientWidth\n      delta = deltaX\n\n      hasOverflow = hasOverflowX\n      isScrollable = isScrollableX\n    } else if (orientation === 'y') {\n      scroll = node.scrollTop\n      maxScroll = scrollHeight - clientHeight\n      delta = deltaY\n\n      hasOverflow = hasOverflowY\n      isScrollable = isScrollableY\n    } else {\n      return false\n    }\n\n    const willScroll = delta > 0 ? scroll < maxScroll : scroll > 0\n\n    return willScroll && hasOverflow && isScrollable\n  }\n\n  /**\n   * The root element on which lenis is instanced\n   */\n  get rootElement() {\n    return (\n      this.options.wrapper === window\n        ? document.documentElement\n        : this.options.wrapper\n    ) as HTMLElement\n  }\n\n  /**\n   * The limit which is the maximum scroll value\n   */\n  get limit() {\n    if (this.options.__experimental__naiveDimensions) {\n      if (this.isHorizontal) {\n        return this.rootElement.scrollWidth - this.rootElement.clientWidth\n      } else {\n        return this.rootElement.scrollHeight - this.rootElement.clientHeight\n      }\n    } else {\n      return this.dimensions.limit[this.isHorizontal ? 'x' : 'y']\n    }\n  }\n\n  /**\n   * Whether or not the scroll is horizontal\n   */\n  get isHorizontal() {\n    return this.options.orientation === 'horizontal'\n  }\n\n  /**\n   * The actual scroll value\n   */\n  get actualScroll() {\n    // value browser takes into account\n    // it has to be this way because of DOCTYPE declaration\n    const wrapper = this.options.wrapper as Window | HTMLElement\n\n    return this.isHorizontal\n      ? (wrapper as Window).scrollX ?? (wrapper as HTMLElement).scrollLeft\n      : (wrapper as Window).scrollY ?? (wrapper as HTMLElement).scrollTop\n  }\n\n  /**\n   * The current scroll value\n   */\n  get scroll() {\n    return this.options.infinite\n      ? modulo(this.animatedScroll, this.limit)\n      : this.animatedScroll\n  }\n\n  /**\n   * The progress of the scroll relative to the limit\n   */\n  get progress() {\n    // avoid progress to be NaN\n    return this.limit === 0 ? 1 : this.scroll / this.limit\n  }\n\n  /**\n   * Current scroll state\n   */\n  get isScrolling() {\n    return this._isScrolling\n  }\n\n  private set isScrolling(value: Scrolling) {\n    if (this._isScrolling !== value) {\n      this._isScrolling = value\n      this.updateClassName()\n    }\n  }\n\n  /**\n   * Check if lenis is stopped\n   */\n  get isStopped() {\n    return this._isStopped\n  }\n\n  private set isStopped(value: boolean) {\n    if (this._isStopped !== value) {\n      this._isStopped = value\n      this.updateClassName()\n    }\n  }\n\n  /**\n   * Check if lenis is locked\n   */\n  get isLocked() {\n    return this._isLocked\n  }\n\n  private set isLocked(value: boolean) {\n    if (this._isLocked !== value) {\n      this._isLocked = value\n      this.updateClassName()\n    }\n  }\n\n  /**\n   * Check if lenis is smooth scrolling\n   */\n  get isSmooth() {\n    return this.isScrolling === 'smooth'\n  }\n\n  /**\n   * The class name applied to the wrapper element\n   */\n  get className() {\n    let className = 'lenis'\n    if (this.options.autoToggle) className += ' lenis-autoToggle'\n    if (this.isStopped) className += ' lenis-stopped'\n    if (this.isLocked) className += ' lenis-locked'\n    if (this.isScrolling) className += ' lenis-scrolling'\n    if (this.isScrolling === 'smooth') className += ' lenis-smooth'\n    return className\n  }\n\n  private updateClassName() {\n    this.cleanUpClassName()\n\n    this.rootElement.className =\n      `${this.rootElement.className} ${this.className}`.trim()\n  }\n\n  private cleanUpClassName() {\n    this.rootElement.className = this.rootElement.className\n      .replace(/lenis(-\\w+)?/g, '')\n      .trim()\n  }\n}\n"], "names": ["lerp", "lerp"], "mappings": ";;;;;AAEE,IAAA,UAAW;;ACMN,SAAS,MAAM,GAAA,EAAa,KAAA,EAAe,GAAA,EAAa;IAC7D,OAAO,KAAK,GAAA,CAAI,KAAK,KAAK,GAAA,CAAI,OAAO,GAAG,CAAC;AAC3C;AAqBO,SAAS,KAAK,CAAA,EAAW,CAAA,EAAW,CAAA,EAAW;IACpD,OAAA,CAAQ,IAAI,CAAA,IAAK,IAAI,IAAI;AAC3B;AAYO,SAAS,KAAK,CAAA,EAAW,CAAA,EAAW,MAAA,EAAgB,SAAA,EAAmB;IAC5E,OAAO,KAAK,GAAG,GAAG,IAAI,KAAK,GAAA,CAAI,CAAC,SAAS,SAAS,CAAC;AACrD;AAUO,SAAS,OAAO,CAAA,EAAW,CAAA,EAAW;IAC3C,OAAA,CAAS,IAAI,IAAK,CAAA,IAAK;AACzB;;AChDO,IAAM,UAAN,MAAc;IACnB,YAAY,MAAA;IACZ,QAAQ,EAAA;IACR,OAAO,EAAA;IACP,KAAK,EAAA;IACL,cAAc,EAAA;IAAA,8CAAA;IAGd,KAAA;IACA,SAAA;IACA,OAAA;IACA,SAAA;IAAA;;;;GAAA,GAOA,QAAQ,SAAA,EAAmB;QACzB,IAAI,CAAC,IAAA,CAAK,SAAA,CAAW,CAAA;QAErB,IAAI,YAAY;QAEhB,IAAI,IAAA,CAAK,QAAA,IAAY,IAAA,CAAK,MAAA,EAAQ;YAChC,IAAA,CAAK,WAAA,IAAe;YACpB,MAAM,iBAAiB,MAAM,GAAG,IAAA,CAAK,WAAA,GAAc,IAAA,CAAK,QAAA,EAAU,CAAC;YAEnE,YAAY,kBAAkB;YAC9B,MAAM,gBAAgB,YAAY,IAAI,IAAA,CAAK,MAAA,CAAO,cAAc;YAChE,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,IAAA,GAAA,CAAQ,IAAA,CAAK,EAAA,GAAK,IAAA,CAAK,IAAA,IAAQ;QACnD,OAAA,IAAW,IAAA,CAAK,IAAA,EAAM;YACpB,IAAA,CAAK,KAAA,GAAQ,KAAK,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,EAAA,EAAI,IAAA,CAAK,IAAA,GAAO,IAAI,SAAS;YAChE,IAAI,KAAK,KAAA,CAAM,IAAA,CAAK,KAAK,MAAM,IAAA,CAAK,EAAA,EAAI;gBACtC,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,EAAA;gBAClB,YAAY;YACd;QACF,OAAO;YAEL,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,EAAA;YAClB,YAAY;QACd;QAEA,IAAI,WAAW;YACb,IAAA,CAAK,IAAA,CAAK;QACZ;QAGA,IAAA,CAAK,QAAA,GAAW,IAAA,CAAK,KAAA,EAAO,SAAS;IACvC;IAAA,uBAAA,GAGA,OAAO;QACL,IAAA,CAAK,SAAA,GAAY;IACnB;IAAA;;;;;;;GAAA,GAUA,OACE,IAAA,EACA,EAAA,EACA,EAAE,MAAAA,KAAAA,EAAM,QAAA,EAAU,MAAA,EAAQ,OAAA,EAAS,QAAA,CAAS,CAAA,EAC5C;QACA,IAAA,CAAK,IAAA,GAAO,IAAA,CAAK,KAAA,GAAQ;QACzB,IAAA,CAAK,EAAA,GAAK;QACV,IAAA,CAAK,IAAA,GAAOA;QACZ,IAAA,CAAK,QAAA,GAAW;QAChB,IAAA,CAAK,MAAA,GAAS;QACd,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,SAAA,GAAY;QAEjB,UAAU;QACV,IAAA,CAAK,QAAA,GAAW;IAClB;AACF;;AC1FO,SAAS,SACd,QAAA,EACA,KAAA,EACA;IACA,IAAI;IACJ,OAAO,SAAA,GAAyB,IAAA,EAAmC;QACjE,IAAI,UAAU,IAAA;QACd,aAAa,KAAK;QAClB,QAAQ,WAAW,MAAM;YACvB,QAAQ,KAAA;YACR,SAAS,KAAA,CAAM,SAAS,IAAI;QAC9B,GAAG,KAAK;IACV;AACF;;ACFO,IAAM,aAAN,MAAiB;IAWtB,YACU,OAAA,EACA,OAAA,EACR,EAAE,aAAa,IAAA,EAAM,UAAU,gBAAgB,GAAA,CAAI,CAAA,GAAI,CAAC,CAAA,CACxD;QAHQ,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QAGR,IAAI,YAAY;YACd,IAAA,CAAK,eAAA,GAAkB,SAAS,IAAA,CAAK,MAAA,EAAQ,aAAa;YAE1D,IAAI,IAAA,CAAK,OAAA,YAAmB,QAAQ;gBAClC,OAAO,gBAAA,CAAiB,UAAU,IAAA,CAAK,eAAA,EAAiB,KAAK;YAC/D,OAAO;gBACL,IAAA,CAAK,qBAAA,GAAwB,IAAI,eAAe,IAAA,CAAK,eAAe;gBACpE,IAAA,CAAK,qBAAA,CAAsB,OAAA,CAAQ,IAAA,CAAK,OAAO;YACjD;YAEA,IAAA,CAAK,qBAAA,GAAwB,IAAI,eAAe,IAAA,CAAK,eAAe;YACpE,IAAA,CAAK,qBAAA,CAAsB,OAAA,CAAQ,IAAA,CAAK,OAAO;QACjD;QAEA,IAAA,CAAK,MAAA,CAAO;IACd;IA9BA,QAAQ,EAAA;IACR,SAAS,EAAA;IACT,eAAe,EAAA;IACf,cAAc,EAAA;IAAA,sFAAA;IAGN,gBAAA;IACA,sBAAA;IACA,sBAAA;IAwBR,UAAU;QACR,IAAA,CAAK,qBAAA,EAAuB,WAAW;QACvC,IAAA,CAAK,qBAAA,EAAuB,WAAW;QAEvC,IAAI,IAAA,CAAK,OAAA,KAAY,UAAU,IAAA,CAAK,eAAA,EAAiB;YACnD,OAAO,mBAAA,CAAoB,UAAU,IAAA,CAAK,eAAA,EAAiB,KAAK;QAClE;IACF;IAEA,SAAS,MAAM;QACb,IAAA,CAAK,eAAA,CAAgB;QACrB,IAAA,CAAK,eAAA,CAAgB;IACvB,EAAA;IAEA,kBAAkB,MAAM;QACtB,IAAI,IAAA,CAAK,OAAA,YAAmB,QAAQ;YAClC,IAAA,CAAK,KAAA,GAAQ,OAAO,UAAA;YACpB,IAAA,CAAK,MAAA,GAAS,OAAO,WAAA;QACvB,OAAO;YACL,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,OAAA,CAAQ,WAAA;YAC1B,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,OAAA,CAAQ,YAAA;QAC7B;IACF,EAAA;IAEA,kBAAkB,MAAM;QACtB,IAAI,IAAA,CAAK,OAAA,YAAmB,QAAQ;YAClC,IAAA,CAAK,YAAA,GAAe,IAAA,CAAK,OAAA,CAAQ,YAAA;YACjC,IAAA,CAAK,WAAA,GAAc,IAAA,CAAK,OAAA,CAAQ,WAAA;QAClC,OAAO;YACL,IAAA,CAAK,YAAA,GAAe,IAAA,CAAK,OAAA,CAAQ,YAAA;YACjC,IAAA,CAAK,WAAA,GAAc,IAAA,CAAK,OAAA,CAAQ,WAAA;QAClC;IACF,EAAA;IAEA,IAAI,QAAQ;QACV,OAAO;YACL,GAAG,IAAA,CAAK,WAAA,GAAc,IAAA,CAAK,KAAA;YAC3B,GAAG,IAAA,CAAK,YAAA,GAAe,IAAA,CAAK,MAAA;QAC9B;IACF;AACF;;AC3EO,IAAM,UAAN,MAAc;IACX,SAGJ,CAAC,EAAA;IAAA;;;;GAAA,GAOL,KAAK,KAAA,EAAA,GAAkB,IAAA,EAAiB;QACtC,IAAI,YAAY,IAAA,CAAK,MAAA,CAAO,KAAK,CAAA,IAAK,CAAC,CAAA;QACvC,IAAA,IAAS,IAAI,GAAG,SAAS,UAAU,MAAA,EAAQ,IAAI,QAAQ,IAAK;YAC1D,SAAA,CAAU,CAAC,CAAA,GAAI,GAAG,IAAI;QACxB;IACF;IAAA;;;;;GAAA,GAQA,GAAwC,KAAA,EAAe,EAAA,EAAQ;QAE7D,IAAA,CAAK,MAAA,CAAO,KAAK,CAAA,EAAG,KAAK,EAAE,KAAA,CAAM,IAAA,CAAK,MAAA,CAAO,KAAK,CAAA,GAAI;YAAC,EAAE;SAAA;QAGzD,OAAO,MAAM;YACX,IAAA,CAAK,MAAA,CAAO,KAAK,CAAA,GAAI,IAAA,CAAK,MAAA,CAAO,KAAK,CAAA,EAAG,OAAO,CAAC,IAAM,OAAO,CAAC;QACjE;IACF;IAAA;;;;GAAA,GAOA,IAAyC,KAAA,EAAe,QAAA,EAAc;QACpE,IAAA,CAAK,MAAA,CAAO,KAAK,CAAA,GAAI,IAAA,CAAK,MAAA,CAAO,KAAK,CAAA,EAAG,OAAO,CAAC,IAAM,aAAa,CAAC;IACvE;IAAA;;GAAA,GAKA,UAAU;QACR,IAAA,CAAK,MAAA,GAAS,CAAC;IACjB;AACF;;ACvDA,IAAM,cAAc,MAAM;AAC1B,IAAM,kBAA2C;IAAE,SAAS;AAAM;AAE3D,IAAM,gBAAN,MAAoB;IAezB,YACU,OAAA,EACA,UAAU;QAAE,iBAAiB;QAAG,iBAAiB;IAAE,CAAA,CAC3D;QAFQ,IAAA,CAAA,OAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;QAER,OAAO,gBAAA,CAAiB,UAAU,IAAA,CAAK,cAAA,EAAgB,KAAK;QAC5D,IAAA,CAAK,cAAA,CAAe;QAEpB,IAAA,CAAK,OAAA,CAAQ,gBAAA,CAAiB,SAAS,IAAA,CAAK,OAAA,EAAS,eAAe;QACpE,IAAA,CAAK,OAAA,CAAQ,gBAAA,CACX,cACA,IAAA,CAAK,YAAA,EACL;QAEF,IAAA,CAAK,OAAA,CAAQ,gBAAA,CACX,aACA,IAAA,CAAK,WAAA,EACL;QAEF,IAAA,CAAK,OAAA,CAAQ,gBAAA,CAAiB,YAAY,IAAA,CAAK,UAAA,EAAY,eAAe;IAC5E;IAjCA,aAAa;QACX,GAAG;QACH,GAAG;IACL,EAAA;IACA,YAAY;QACV,GAAG;QACH,GAAG;IACL,EAAA;IACA,SAAS;QACP,OAAO;QACP,QAAQ;IACV,EAAA;IACQ,UAAU,IAAI,QAAQ,EAAA;IAAA;;;;;GAAA,GA6B9B,GAAG,KAAA,EAAe,QAAA,EAAiC;QACjD,OAAO,IAAA,CAAK,OAAA,CAAQ,EAAA,CAAG,OAAO,QAAQ;IACxC;IAAA,4CAAA,GAGA,UAAU;QACR,IAAA,CAAK,OAAA,CAAQ,OAAA,CAAQ;QAErB,OAAO,mBAAA,CAAoB,UAAU,IAAA,CAAK,cAAA,EAAgB,KAAK;QAE/D,IAAA,CAAK,OAAA,CAAQ,mBAAA,CAAoB,SAAS,IAAA,CAAK,OAAA,EAAS,eAAe;QACvE,IAAA,CAAK,OAAA,CAAQ,mBAAA,CACX,cACA,IAAA,CAAK,YAAA,EACL;QAEF,IAAA,CAAK,OAAA,CAAQ,mBAAA,CACX,aACA,IAAA,CAAK,WAAA,EACL;QAEF,IAAA,CAAK,OAAA,CAAQ,mBAAA,CACX,YACA,IAAA,CAAK,UAAA,EACL;IAEJ;IAAA;;;;GAAA,GAOA,eAAe,CAAC,UAAsB;QAEpC,MAAM,EAAE,OAAA,EAAS,OAAA,CAAQ,CAAA,GAAI,MAAM,aAAA,GAC/B,MAAM,aAAA,CAAc,CAAC,CAAA,GACrB;QAEJ,IAAA,CAAK,UAAA,CAAW,CAAA,GAAI;QACpB,IAAA,CAAK,UAAA,CAAW,CAAA,GAAI;QAEpB,IAAA,CAAK,SAAA,GAAY;YACf,GAAG;YACH,GAAG;QACL;QAEA,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,UAAU;YAC1B,QAAQ;YACR,QAAQ;YACR;QACF,CAAC;IACH,EAAA;IAAA,wCAAA,GAGA,cAAc,CAAC,UAAsB;QAEnC,MAAM,EAAE,OAAA,EAAS,OAAA,CAAQ,CAAA,GAAI,MAAM,aAAA,GAC/B,MAAM,aAAA,CAAc,CAAC,CAAA,GACrB;QAEJ,MAAM,SAAS,CAAA,CAAE,UAAU,IAAA,CAAK,UAAA,CAAW,CAAA,IAAK,IAAA,CAAK,OAAA,CAAQ,eAAA;QAC7D,MAAM,SAAS,CAAA,CAAE,UAAU,IAAA,CAAK,UAAA,CAAW,CAAA,IAAK,IAAA,CAAK,OAAA,CAAQ,eAAA;QAE7D,IAAA,CAAK,UAAA,CAAW,CAAA,GAAI;QACpB,IAAA,CAAK,UAAA,CAAW,CAAA,GAAI;QAEpB,IAAA,CAAK,SAAA,GAAY;YACf,GAAG;YACH,GAAG;QACL;QAEA,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,UAAU;YAC1B;YACA;YACA;QACF,CAAC;IACH,EAAA;IAEA,aAAa,CAAC,UAAsB;QAClC,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,UAAU;YAC1B,QAAQ,IAAA,CAAK,SAAA,CAAU,CAAA;YACvB,QAAQ,IAAA,CAAK,SAAA,CAAU,CAAA;YACvB;QACF,CAAC;IACH,EAAA;IAAA,oCAAA,GAGA,UAAU,CAAC,UAAsB;QAC/B,IAAI,EAAE,MAAA,EAAQ,MAAA,EAAQ,SAAA,CAAU,CAAA,GAAI;QAEpC,MAAM,cACJ,cAAc,IAAI,cAAc,cAAc,IAAI,IAAA,CAAK,MAAA,CAAO,KAAA,GAAQ;QACxE,MAAM,cACJ,cAAc,IAAI,cAAc,cAAc,IAAI,IAAA,CAAK,MAAA,CAAO,MAAA,GAAS;QAEzE,UAAU;QACV,UAAU;QAEV,UAAU,IAAA,CAAK,OAAA,CAAQ,eAAA;QACvB,UAAU,IAAA,CAAK,OAAA,CAAQ,eAAA;QAEvB,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,UAAU;YAAE;YAAQ;YAAQ;QAAM,CAAC;IACvD,EAAA;IAEA,iBAAiB,MAAM;QACrB,IAAA,CAAK,MAAA,GAAS;YACZ,OAAO,OAAO,UAAA;YACd,QAAQ,OAAO,WAAA;QACjB;IACF,EAAA;AACF;;ACpIA,IAAM,gBAAgB,CAAC,IAAc,KAAK,GAAA,CAAI,GAAG,QAAQ,KAAK,GAAA,CAAI,GAAG,CAAA,KAAM,CAAC,CAAC;AAEtE,IAAM,QAAN,MAAY;IACT,eAA0B,MAAA;IAAA,gCAAA;IAC1B,aAAa,MAAA;IAAA,8EAAA;IACb,YAAY,MAAA;IAAA,oEAAA;IACZ,gCAAgC,MAAA;IAChC,wBAA8D,KAAA;IAC9D,UAAyB,KAAA;IAAA;;GAAA,GAKjC,WAAA;IAAA;;GAAA,GAIA,OAAO,EAAA;IAAA;;;;;;;;;GAAA,GAWP,WAAqB,CAAC,EAAA;IAAA;;GAAA,GAItB,eAAe,EAAA;IAAA;;GAAA,GAIf,WAAW,EAAA;IAAA;;GAAA,GAIX,YAAwB,EAAA;IAAA;;GAAA,GAIxB,QAAA;IAAA;;GAAA,GAOA,aAAA;IAAA;;GAAA,GAIA,eAAA;IAAA,8EAAA;IAGiB,UAAU,IAAI,QAAQ,EAAA;IACtB,UAAU,IAAI,QAAQ,EAAA;IAAA,sFAAA;IAE9B,WAAA;IAAA,0DAAA;IACQ,cAAA;IAEjB,YAAY,EACV,UAAU,MAAA,EACV,UAAU,SAAS,eAAA,EACnB,eAAe,OAAA,EACf,cAAc,IAAA,EACd,YAAY,KAAA,EACZ,gBAAgB,KAAA,EAChB,uBAAuB,GAAA,EACvB,QAAA,EAAA,aAAA;IACA,MAAA,EACA,MAAAC,QAAO,GAAA,EACP,WAAW,KAAA,EACX,cAAc,UAAA,EAAA,uBAAA;IACd,qBAAqB,UAAA,EAAA,6BAAA;IACrB,kBAAkB,CAAA,EAClB,kBAAkB,CAAA,EAClB,aAAa,IAAA,EACb,OAAA,EACA,aAAA,EACA,aAAa,IAAA,EACb,UAAU,KAAA,EACV,UAAU,KAAA,EACV,aAAa,KAAA,EAAA,kDAAA;IACb,oBAAoB,KAAA,EACpB,kCAAkC,KAAA,EACpC,GAAkB,CAAC,CAAA,CAAG;QAEpB,OAAO,YAAA,GAAe;QAGtB,IAAI,CAAC,WAAW,YAAY,SAAS,eAAA,EAAiB;YACpD,UAAU;QACZ;QAGA,IAAI,OAAO,aAAa,YAAY,OAAO,WAAW,YAAY;YAChE,SAAS;QACX,OAAA,IAAW,OAAO,WAAW,cAAc,OAAO,aAAa,UAAU;YACvE,WAAW;QACb;QAGA,IAAA,CAAK,OAAA,GAAU;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,MAAAA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;QAGA,IAAA,CAAK,UAAA,GAAa,IAAI,WAAW,SAAS,SAAS;YAAE;QAAW,CAAC;QAGjE,IAAA,CAAK,eAAA,CAAgB;QAGrB,IAAA,CAAK,YAAA,GAAe,IAAA,CAAK,cAAA,GAAiB,IAAA,CAAK,YAAA;QAG/C,IAAA,CAAK,OAAA,CAAQ,OAAA,CAAQ,gBAAA,CAAiB,UAAU,IAAA,CAAK,cAAA,EAAgB,KAAK;QAE1E,IAAA,CAAK,OAAA,CAAQ,OAAA,CAAQ,gBAAA,CAAiB,aAAa,IAAA,CAAK,WAAA,EAAa;YACnE,SAAS;QACX,CAAC;QAED,IAAI,IAAA,CAAK,OAAA,CAAQ,OAAA,IAAW,IAAA,CAAK,OAAA,CAAQ,OAAA,KAAY,QAAQ;YAC3D,IAAA,CAAK,OAAA,CAAQ,OAAA,CAAQ,gBAAA,CACnB,SACA,IAAA,CAAK,OAAA,EACL;QAEJ;QAEA,IAAA,CAAK,OAAA,CAAQ,OAAA,CAAQ,gBAAA,CACnB,eACA,IAAA,CAAK,aAAA,EACL;QAIF,IAAA,CAAK,aAAA,GAAgB,IAAI,cAAc,cAA6B;YAClE;YACA;QACF,CAAC;QACD,IAAA,CAAK,aAAA,CAAc,EAAA,CAAG,UAAU,IAAA,CAAK,eAAe;QAEpD,IAAI,IAAA,CAAK,OAAA,CAAQ,UAAA,EAAY;YAC3B,IAAA,CAAK,WAAA,CAAY,gBAAA,CAAiB,iBAAiB,IAAA,CAAK,eAAA,EAAiB;gBACvE,SAAS;YACX,CAAC;QACH;QAEA,IAAI,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS;YACxB,IAAA,CAAK,OAAA,GAAU,sBAAsB,IAAA,CAAK,GAAG;QAC/C;IACF;IAAA;;GAAA,GAKA,UAAU;QACR,IAAA,CAAK,OAAA,CAAQ,OAAA,CAAQ;QAErB,IAAA,CAAK,OAAA,CAAQ,OAAA,CAAQ,mBAAA,CACnB,UACA,IAAA,CAAK,cAAA,EACL;QAGF,IAAA,CAAK,OAAA,CAAQ,OAAA,CAAQ,mBAAA,CAAoB,aAAa,IAAA,CAAK,WAAA,EAAa;YACtE,SAAS;QACX,CAAC;QAED,IAAA,CAAK,OAAA,CAAQ,OAAA,CAAQ,mBAAA,CACnB,eACA,IAAA,CAAK,aAAA,EACL;QAGF,IAAI,IAAA,CAAK,OAAA,CAAQ,OAAA,IAAW,IAAA,CAAK,OAAA,CAAQ,OAAA,KAAY,QAAQ;YAC3D,IAAA,CAAK,OAAA,CAAQ,OAAA,CAAQ,mBAAA,CACnB,SACA,IAAA,CAAK,OAAA,EACL;QAEJ;QAEA,IAAA,CAAK,aAAA,CAAc,OAAA,CAAQ;QAC3B,IAAA,CAAK,UAAA,CAAW,OAAA,CAAQ;QAExB,IAAA,CAAK,gBAAA,CAAiB;QAEtB,IAAI,IAAA,CAAK,OAAA,EAAS;YAChB,qBAAqB,IAAA,CAAK,OAAO;QACnC;IACF;IAWA,GAAG,KAAA,EAAmB,QAAA,EAAe;QACnC,OAAO,IAAA,CAAK,OAAA,CAAQ,EAAA,CAAG,OAAO,QAAQ;IACxC;IAUA,IAAI,KAAA,EAAmB,QAAA,EAAe;QACpC,OAAO,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,OAAO,QAAQ;IACzC;IAEQ,cAAc,CAAC,MAA2B;QAChD,IAAI,CAAA,CAAE,aAAa,WAAA,GAAc;YAC/B,IAAI,IAAA,CAAK,WAAA,KAAgB,YAAY,IAAA,CAAK,WAAA,KAAgB,OAAO;gBAC/D,EAAE,eAAA,CAAgB;YACpB;QACF;IACF,EAAA;IAEQ,yBAAyB,MAAM;QACrC,IAAA,CAAK,OAAA,CAAQ,OAAA,CAAQ,aAAA,CACnB,IAAI,YAAY,aAAa;YAC3B,SAAS,IAAA,CAAK,OAAA,CAAQ,OAAA,KAAY;YAAA,qBAAA;YAElC,QAAQ;gBACN,gBAAgB;YAClB;QACF,CAAC;IAEL,EAAA;IAEQ,kBAAkB,CAAC,UAA2B;QACpD,IAAI,MAAM,YAAA,CAAa,QAAA,CAAS,UAAU,GAAG;YAC3C,MAAM,WAAW,IAAA,CAAK,YAAA,GAAe,eAAe;YAEpD,MAAM,WAAW,iBAAiB,IAAA,CAAK,WAAW,CAAA,CAChD,QACF,CAAA;YAEA,IAAI;gBAAC;gBAAU,MAAM;aAAA,CAAE,QAAA,CAAS,QAAQ,GAAG;gBACzC,IAAA,CAAK,YAAA,CAAa;YACpB,OAAO;gBACL,IAAA,CAAK,aAAA,CAAc;YACrB;QACF;IACF,EAAA;IAEQ,UAAU,MAAA,EAAgB;QAGhC,IAAI,IAAA,CAAK,YAAA,EAAc;YACrB,IAAA,CAAK,OAAA,CAAQ,OAAA,CAAQ,QAAA,CAAS;gBAAE,MAAM;gBAAQ,UAAU;YAAU,CAAC;QACrE,OAAO;YACL,IAAA,CAAK,OAAA,CAAQ,OAAA,CAAQ,QAAA,CAAS;gBAAE,KAAK;gBAAQ,UAAU;YAAU,CAAC;QACpE;IACF;IAEQ,UAAU,CAAC,UAAqC;QACtD,MAAM,OAAO,MAAM,YAAA,CAAa;QAChC,MAAM,SAAS,KAAK,IAAA,CAClB,CAAC,OACC,gBAAgB,qBAAA,CACf,KAAK,YAAA,CAAa,MAAM,GAAG,WAAW,GAAG,KACxC,KAAK,YAAA,CAAa,MAAM,GAAG,WAAW,IAAI,KAC1C,KAAK,YAAA,CAAa,MAAM,GAAG,WAAW,KAAK,CAAA;QAEjD,IAAI,QAAQ;YACV,MAAM,KAAK,OAAO,YAAA,CAAa,MAAM;YAErC,IAAI,IAAI;gBACN,MAAM,UACJ,OAAO,IAAA,CAAK,OAAA,CAAQ,OAAA,KAAY,YAAY,IAAA,CAAK,OAAA,CAAQ,OAAA,GACrD,IAAA,CAAK,OAAA,CAAQ,OAAA,GACb,KAAA;gBAEN,IAAI,SAA0B,CAAA,CAAA,EAAI,GAAG,KAAA,CAAM,GAAG,CAAA,CAAE,CAAC,CAAC,EAAA;gBAClD,IAAI;oBAAC;oBAAK;oBAAM;oBAAO;oBAAQ;oBAAS,QAAQ;iBAAA,CAAE,QAAA,CAAS,EAAE,GAAG;oBAC9D,SAAS;gBACX;gBAEA,IAAA,CAAK,QAAA,CAAS,QAAQ,OAAO;YAC/B;QACF;IACF,EAAA;IAEQ,gBAAgB,CAAC,UAAqC;QAC5D,IAAI,MAAM,MAAA,KAAW,GAAG;YACtB,IAAA,CAAK,KAAA,CAAM;QACb;IACF,EAAA;IAEQ,kBAAkB,CAAC,SAA4B;QACrD,IACE,OAAO,IAAA,CAAK,OAAA,CAAQ,aAAA,KAAkB,cACtC,IAAA,CAAK,OAAA,CAAQ,aAAA,CAAc,IAAI,MAAM,OAErC;QAEF,MAAM,EAAE,MAAA,EAAQ,MAAA,EAAQ,KAAA,CAAM,CAAA,GAAI;QAElC,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,kBAAkB;YAAE;YAAQ;YAAQ;QAAM,CAAC;QAG7D,IAAI,MAAM,OAAA,CAAS,CAAA;QAEnB,IAAI,MAAM,oBAAA,CAAsB,CAAA;QAEhC,MAAM,UAAU,MAAM,IAAA,CAAK,QAAA,CAAS,OAAO;QAC3C,MAAM,UAAU,MAAM,IAAA,CAAK,QAAA,CAAS,OAAO;QAE3C,IAAA,CAAK,UAAA,GAAa,MAAM,IAAA,KAAS,gBAAgB,MAAM,IAAA,KAAS;QAWhE,MAAM,eAAe,WAAW,KAAK,WAAW;QAEhD,MAAM,cACJ,IAAA,CAAK,OAAA,CAAQ,SAAA,IACb,WACA,MAAM,IAAA,KAAS,gBACf,gBACA,CAAC,IAAA,CAAK,SAAA,IACN,CAAC,IAAA,CAAK,QAAA;QAER,IAAI,aAAa;YACf,IAAA,CAAK,KAAA,CAAM;YACX;QACF;QAQA,MAAM,mBACH,IAAA,CAAK,OAAA,CAAQ,kBAAA,KAAuB,cAAc,WAAW,KAC7D,IAAA,CAAK,OAAA,CAAQ,kBAAA,KAAuB,gBAAgB,WAAW;QAElE,IAAI,gBAAgB,kBAAkB;YAEpC;QACF;QAGA,IAAI,eAAe,MAAM,YAAA,CAAa;QACtC,eAAe,aAAa,KAAA,CAAM,GAAG,aAAa,OAAA,CAAQ,IAAA,CAAK,WAAW,CAAC;QAE3E,MAAM,UAAU,IAAA,CAAK,OAAA,CAAQ,OAAA;QAE7B,IACE,CAAC,CAAC,aAAa,IAAA,CACb,CAAC,OACC,gBAAgB,eAAA,CACd,OAAO,YAAY,cAAc,UAAU,IAAI,KAC/C,KAAK,YAAA,GAAe,oBAAoB,KACvC,WAAW,KAAK,YAAA,GAAe,0BAA0B,KACzD,WAAW,KAAK,YAAA,GAAe,0BAA0B,KACzD,IAAA,CAAK,OAAA,CAAQ,iBAAA,IACZ,IAAA,CAAK,iBAAA,CAAkB,MAAM;gBAAE;gBAAQ;YAAO,CAAC,CAAA,IAGvD;QAEF,IAAI,IAAA,CAAK,SAAA,IAAa,IAAA,CAAK,QAAA,EAAU;YACnC,IAAI,MAAM,UAAA,EAAY;gBACpB,MAAM,cAAA,CAAe;YACvB;YACA;QACF;QAEA,MAAM,WACH,IAAA,CAAK,OAAA,CAAQ,SAAA,IAAa,WAC1B,IAAA,CAAK,OAAA,CAAQ,WAAA,IAAe;QAE/B,IAAI,CAAC,UAAU;YACb,IAAA,CAAK,WAAA,GAAc;YACnB,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK;YAElB,MAAM,oBAAA,GAAuB;YAC7B;QACF;QAEA,IAAI,QAAQ;QACZ,IAAI,IAAA,CAAK,OAAA,CAAQ,kBAAA,KAAuB,QAAQ;YAC9C,QAAQ,KAAK,GAAA,CAAI,MAAM,IAAI,KAAK,GAAA,CAAI,MAAM,IAAI,SAAS;QACzD,OAAA,IAAW,IAAA,CAAK,OAAA,CAAQ,kBAAA,KAAuB,cAAc;YAC3D,QAAQ;QACV;QAEA,IACE,CAAC,IAAA,CAAK,OAAA,CAAQ,UAAA,IACd,IAAA,CAAK,OAAA,CAAQ,QAAA,IACZ,IAAA,CAAK,OAAA,CAAQ,OAAA,KAAY,UAAA,CACtB,IAAA,CAAK,cAAA,GAAiB,KAAK,IAAA,CAAK,cAAA,GAAiB,IAAA,CAAK,KAAA,IACrD,IAAA,CAAK,cAAA,KAAmB,KAAK,SAAS,KACtC,IAAA,CAAK,cAAA,KAAmB,IAAA,CAAK,KAAA,IAAS,SAAS,CAAA,GACpD;YAEA,MAAM,oBAAA,GAAuB;QAE/B;QAEA,IAAI,MAAM,UAAA,EAAY;YACpB,MAAM,cAAA,CAAe;QACvB;QAEA,MAAM,cAAc,WAAW,IAAA,CAAK,OAAA,CAAQ,SAAA;QAC5C,MAAM,aAAa,WAAW,MAAM,IAAA,KAAS;QAE7C,MAAM,kBAAkB;QAExB,IAAI,iBAAiB;YAEnB,QACE,KAAK,IAAA,CAAK,IAAA,CAAK,QAAQ,IACvB,KAAK,GAAA,CAAI,KAAK,GAAA,CAAI,IAAA,CAAK,QAAQ,GAAG,IAAA,CAAK,OAAA,CAAQ,oBAAoB;QACvE;QAEA,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,YAAA,GAAe,OAAO;YACvC,cAAc;YACd,GAAI,cACA;gBACE,MAAM,kBAAkB,IAAA,CAAK,OAAA,CAAQ,aAAA,GAAgB;YAEvD,IACA;gBACE,MAAM,IAAA,CAAK,OAAA,CAAQ,IAAA;gBACnB,UAAU,IAAA,CAAK,OAAA,CAAQ,QAAA;gBACvB,QAAQ,IAAA,CAAK,OAAA,CAAQ,MAAA;YACvB,CAAA;QACN,CAAC;IACH,EAAA;IAAA;;GAAA,GAKA,SAAS;QACP,IAAA,CAAK,UAAA,CAAW,MAAA,CAAO;QACvB,IAAA,CAAK,cAAA,GAAiB,IAAA,CAAK,YAAA,GAAe,IAAA,CAAK,YAAA;QAC/C,IAAA,CAAK,IAAA,CAAK;IACZ;IAEQ,OAAO;QACb,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,UAAU,IAAI;IAClC;IAEQ,iBAAiB,MAAM;QAC7B,IAAI,IAAA,CAAK,qBAAA,KAA0B,MAAM;YACvC,aAAa,IAAA,CAAK,qBAAqB;YACvC,IAAA,CAAK,qBAAA,GAAwB;QAC/B;QAEA,IAAI,IAAA,CAAK,6BAAA,EAA+B;YACtC,IAAA,CAAK,6BAAA,GAAgC;YACrC;QACF;QAEA,IAAI,IAAA,CAAK,WAAA,KAAgB,SAAS,IAAA,CAAK,WAAA,KAAgB,UAAU;YAC/D,MAAM,aAAa,IAAA,CAAK,cAAA;YACxB,IAAA,CAAK,cAAA,GAAiB,IAAA,CAAK,YAAA,GAAe,IAAA,CAAK,YAAA;YAC/C,IAAA,CAAK,YAAA,GAAe,IAAA,CAAK,QAAA;YACzB,IAAA,CAAK,QAAA,GAAW,IAAA,CAAK,cAAA,GAAiB;YACtC,IAAA,CAAK,SAAA,GAAY,KAAK,IAAA,CACpB,IAAA,CAAK,cAAA,GAAiB;YAGxB,IAAI,CAAC,IAAA,CAAK,SAAA,EAAW;gBACnB,IAAA,CAAK,WAAA,GAAc;YACrB;YAEA,IAAA,CAAK,IAAA,CAAK;YAEV,IAAI,IAAA,CAAK,QAAA,KAAa,GAAG;gBACvB,IAAA,CAAK,qBAAA,GAAwB,WAAW,MAAM;oBAC5C,IAAA,CAAK,YAAA,GAAe,IAAA,CAAK,QAAA;oBACzB,IAAA,CAAK,QAAA,GAAW;oBAChB,IAAA,CAAK,WAAA,GAAc;oBACnB,IAAA,CAAK,IAAA,CAAK;gBACZ,GAAG,GAAG;YACR;QACF;IACF,EAAA;IAEQ,QAAQ;QACd,IAAA,CAAK,QAAA,GAAW;QAChB,IAAA,CAAK,WAAA,GAAc;QACnB,IAAA,CAAK,cAAA,GAAiB,IAAA,CAAK,YAAA,GAAe,IAAA,CAAK,YAAA;QAC/C,IAAA,CAAK,YAAA,GAAe,IAAA,CAAK,QAAA,GAAW;QACpC,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK;IACpB;IAAA;;GAAA,GAKA,QAAQ;QACN,IAAI,CAAC,IAAA,CAAK,SAAA,CAAW,CAAA;QAErB,IAAI,IAAA,CAAK,OAAA,CAAQ,UAAA,EAAY;YAC3B,IAAA,CAAK,WAAA,CAAY,KAAA,CAAM,cAAA,CAAe,UAAU;YAChD;QACF;QAEA,IAAA,CAAK,aAAA,CAAc;IACrB;IAEQ,gBAAgB;QACtB,IAAI,CAAC,IAAA,CAAK,SAAA,CAAW,CAAA;QAErB,IAAA,CAAK,KAAA,CAAM;QACX,IAAA,CAAK,SAAA,GAAY;QACjB,IAAA,CAAK,IAAA,CAAK;IACZ;IAAA;;GAAA,GAKA,OAAO;QACL,IAAI,IAAA,CAAK,SAAA,CAAW,CAAA;QAEpB,IAAI,IAAA,CAAK,OAAA,CAAQ,UAAA,EAAY;YAC3B,IAAA,CAAK,WAAA,CAAY,KAAA,CAAM,WAAA,CAAY,YAAY,MAAM;YACrD;QACF;QAEA,IAAA,CAAK,YAAA,CAAa;IACpB;IAEQ,eAAe;QACrB,IAAI,IAAA,CAAK,SAAA,CAAW,CAAA;QAEpB,IAAA,CAAK,KAAA,CAAM;QACX,IAAA,CAAK,SAAA,GAAY;QACjB,IAAA,CAAK,IAAA,CAAK;IACZ;IAAA;;;;GAAA,GAOA,MAAM,CAAC,SAAiB;QACtB,MAAM,YAAY,OAAA,CAAQ,IAAA,CAAK,IAAA,IAAQ,IAAA;QACvC,IAAA,CAAK,IAAA,GAAO;QAEZ,IAAA,CAAK,OAAA,CAAQ,OAAA,CAAQ,YAAY,IAAK;QAEtC,IAAI,IAAA,CAAK,OAAA,CAAQ,OAAA,EAAS;YACxB,IAAA,CAAK,OAAA,GAAU,sBAAsB,IAAA,CAAK,GAAG;QAC/C;IACF,EAAA;IAAA;;;;;;;;;;;;;;;;;;;GAAA,GAsBA,SACE,MAAA,EACA,EACE,SAAS,CAAA,EACT,YAAY,KAAA,EACZ,OAAO,KAAA,EACP,WAAW,IAAA,CAAK,OAAA,CAAQ,QAAA,EACxB,SAAS,IAAA,CAAK,OAAA,CAAQ,MAAA,EACtB,MAAAA,QAAO,IAAA,CAAK,OAAA,CAAQ,IAAA,EACpB,OAAA,EACA,UAAA,EACA,QAAQ,KAAA,EAAA,yBAAA;IACR,eAAe,IAAA,EAAA,mCAAA;IACf,QAAA,EACF,GAAqB,CAAC,CAAA,EACtB;QACA,IAAA,CAAK,IAAA,CAAK,SAAA,IAAa,IAAA,CAAK,QAAA,KAAa,CAAC,MAAO,CAAA;QAGjD,IACE,OAAO,WAAW,YAClB;YAAC;YAAO;YAAQ,OAAO;SAAA,CAAE,QAAA,CAAS,MAAM,GACxC;YACA,SAAS;QACX,OAAA,IACE,OAAO,WAAW,YAClB;YAAC;YAAU;YAAS,KAAK;SAAA,CAAE,QAAA,CAAS,MAAM,GAC1C;YACA,SAAS,IAAA,CAAK,KAAA;QAChB,OAAO;YACL,IAAI;YAEJ,IAAI,OAAO,WAAW,UAAU;gBAE9B,OAAO,SAAS,aAAA,CAAc,MAAM;YACtC,OAAA,IAAW,kBAAkB,eAAe,QAAQ,UAAU;gBAE5D,OAAO;YACT;YAEA,IAAI,MAAM;gBACR,IAAI,IAAA,CAAK,OAAA,CAAQ,OAAA,KAAY,QAAQ;oBAEnC,MAAM,cAAc,IAAA,CAAK,WAAA,CAAY,qBAAA,CAAsB;oBAC3D,UAAU,IAAA,CAAK,YAAA,GAAe,YAAY,IAAA,GAAO,YAAY,GAAA;gBAC/D;gBAEA,MAAM,OAAO,KAAK,qBAAA,CAAsB;gBAExC,SAAA,CACG,IAAA,CAAK,YAAA,GAAe,KAAK,IAAA,GAAO,KAAK,GAAA,IAAO,IAAA,CAAK,cAAA;YACtD;QACF;QAEA,IAAI,OAAO,WAAW,SAAU,CAAA;QAEhC,UAAU;QACV,SAAS,KAAK,KAAA,CAAM,MAAM;QAE1B,IAAI,IAAA,CAAK,OAAA,CAAQ,QAAA,EAAU;YACzB,IAAI,cAAc;gBAChB,IAAA,CAAK,YAAA,GAAe,IAAA,CAAK,cAAA,GAAiB,IAAA,CAAK,MAAA;gBAE/C,MAAM,WAAW,SAAS,IAAA,CAAK,cAAA;gBAE/B,IAAI,WAAW,IAAA,CAAK,KAAA,GAAQ,GAAG;oBAC7B,SAAS,SAAS,IAAA,CAAK,KAAA;gBACzB,OAAA,IAAW,WAAW,CAAC,IAAA,CAAK,KAAA,GAAQ,GAAG;oBACrC,SAAS,SAAS,IAAA,CAAK,KAAA;gBACzB;YACF;QACF,OAAO;YACL,SAAS,MAAM,GAAG,QAAQ,IAAA,CAAK,KAAK;QACtC;QAEA,IAAI,WAAW,IAAA,CAAK,YAAA,EAAc;YAChC,UAAU,IAAI;YACd,aAAa,IAAI;YACjB;QACF;QAEA,IAAA,CAAK,QAAA,GAAW,YAAY,CAAC;QAE7B,IAAI,WAAW;YACb,IAAA,CAAK,cAAA,GAAiB,IAAA,CAAK,YAAA,GAAe;YAC1C,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,MAAM;YAC1B,IAAA,CAAK,KAAA,CAAM;YACX,IAAA,CAAK,4BAAA,CAA6B;YAClC,IAAA,CAAK,IAAA,CAAK;YACV,aAAa,IAAI;YACjB,IAAA,CAAK,QAAA,GAAW,CAAC;YAEjB,sBAAsB,MAAM;gBAC1B,IAAA,CAAK,sBAAA,CAAuB;YAC9B,CAAC;YACD;QACF;QAEA,IAAI,CAAC,cAAc;YACjB,IAAA,CAAK,YAAA,GAAe;QACtB;QAGA,IAAI,OAAO,aAAa,YAAY,OAAO,WAAW,YAAY;YAChE,SAAS;QACX,OAAA,IAAW,OAAO,WAAW,cAAc,OAAO,aAAa,UAAU;YACvE,WAAW;QACb;QAEA,IAAA,CAAK,OAAA,CAAQ,MAAA,CAAO,IAAA,CAAK,cAAA,EAAgB,QAAQ;YAC/C;YACA;YACA,MAAAA;YACA,SAAS,MAAM;gBAEb,IAAI,KAAM,CAAA,IAAA,CAAK,QAAA,GAAW;gBAC1B,IAAA,CAAK,WAAA,GAAc;gBACnB,UAAU,IAAI;YAChB;YACA,UAAU,CAAC,OAAe,cAAuB;gBAC/C,IAAA,CAAK,WAAA,GAAc;gBAGnB,IAAA,CAAK,YAAA,GAAe,IAAA,CAAK,QAAA;gBACzB,IAAA,CAAK,QAAA,GAAW,QAAQ,IAAA,CAAK,cAAA;gBAC7B,IAAA,CAAK,SAAA,GAAY,KAAK,IAAA,CAAK,IAAA,CAAK,QAAQ;gBAExC,IAAA,CAAK,cAAA,GAAiB;gBACtB,IAAA,CAAK,SAAA,CAAU,IAAA,CAAK,MAAM;gBAE1B,IAAI,cAAc;oBAEhB,IAAA,CAAK,YAAA,GAAe;gBACtB;gBAEA,IAAI,CAAC,UAAW,CAAA,IAAA,CAAK,IAAA,CAAK;gBAE1B,IAAI,WAAW;oBACb,IAAA,CAAK,KAAA,CAAM;oBACX,IAAA,CAAK,IAAA,CAAK;oBACV,aAAa,IAAI;oBACjB,IAAA,CAAK,QAAA,GAAW,CAAC;oBAEjB,sBAAsB,MAAM;wBAC1B,IAAA,CAAK,sBAAA,CAAuB;oBAC9B,CAAC;oBAGD,IAAA,CAAK,4BAAA,CAA6B;gBACpC;YACF;QACF,CAAC;IACH;IAEQ,+BAA+B;QACrC,IAAA,CAAK,6BAAA,GAAgC;QAErC,sBAAsB,MAAM;YAC1B,IAAA,CAAK,6BAAA,GAAgC;QACvC,CAAC;IACH;IAEQ,kBACN,IAAA,EACA,EAAE,MAAA,EAAQ,MAAA,CAAO,CAAA,EACjB;QACA,MAAM,OAAO,KAAK,GAAA,CAAI;QAGtB,MAAM,QAAS,KAAK,MAAA,KAAW,CAAC;QAEhC,IAAI,cACF,cACA,eACA,eACA,aACA,cACA,aACA;QAEF,MAAM,qBAAqB,IAAA,CAAK,OAAA,CAAQ,kBAAA;QAExC,IAAI,OAAA,CAAQ,MAAM,IAAA,IAAQ,CAAA,IAAK,KAAM;YACnC,MAAM,IAAA,GAAO,KAAK,GAAA,CAAI;YAEtB,MAAM,gBAAgB,OAAO,gBAAA,CAAiB,IAAI;YAClD,MAAM,aAAA,GAAgB;YAEtB,MAAM,kBAAkB,cAAc,SAAA;YACtC,MAAM,kBAAkB,cAAc,SAAA;YAEtC,eAAe;gBAAC;gBAAQ;gBAAW,QAAQ;aAAA,CAAE,QAAA,CAAS,eAAe;YACrE,eAAe;gBAAC;gBAAQ;gBAAW,QAAQ;aAAA,CAAE,QAAA,CAAS,eAAe;YACrE,MAAM,YAAA,GAAe;YACrB,MAAM,YAAA,GAAe;YAErB,IAAI,CAAC,gBAAgB,CAAC,aAAc,CAAA,OAAO;YAC3C,IAAI,uBAAuB,cAAc,CAAC,aAAc,CAAA,OAAO;YAC/D,IAAI,uBAAuB,gBAAgB,CAAC,aAAc,CAAA,OAAO;YAEjE,cAAc,KAAK,WAAA;YACnB,eAAe,KAAK,YAAA;YAEpB,cAAc,KAAK,WAAA;YACnB,eAAe,KAAK,YAAA;YAEpB,gBAAgB,cAAc;YAC9B,gBAAgB,eAAe;YAE/B,MAAM,aAAA,GAAgB;YACtB,MAAM,aAAA,GAAgB;YACtB,MAAM,WAAA,GAAc;YACpB,MAAM,YAAA,GAAe;YACrB,MAAM,WAAA,GAAc;YACpB,MAAM,YAAA,GAAe;QACvB,OAAO;YACL,gBAAgB,MAAM,aAAA;YACtB,gBAAgB,MAAM,aAAA;YACtB,eAAe,MAAM,YAAA;YACrB,eAAe,MAAM,YAAA;YACrB,cAAc,MAAM,WAAA;YACpB,eAAe,MAAM,YAAA;YACrB,cAAc,MAAM,WAAA;YACpB,eAAe,MAAM,YAAA;QACvB;QAEA,IACG,CAAC,gBAAgB,CAAC,gBAClB,CAAC,iBAAiB,CAAC,eACpB;YACA,OAAO;QACT;QAEA,IAAI,uBAAuB,cAAA,CAAe,CAAC,gBAAgB,CAAC,aAAA,GAC1D,OAAO;QAET,IACE,uBAAuB,gBAAA,CACtB,CAAC,gBAAgB,CAAC,aAAA,GAEnB,OAAO;QAET,IAAI;QAEJ,IAAI,uBAAuB,cAAc;YACvC,cAAc;QAChB,OAAA,IAAW,uBAAuB,YAAY;YAC5C,cAAc;QAChB,OAAO;YACL,MAAM,eAAe,WAAW;YAChC,MAAM,eAAe,WAAW;YAEhC,IAAI,gBAAgB,gBAAgB,eAAe;gBACjD,cAAc;YAChB;YAEA,IAAI,gBAAgB,gBAAgB,eAAe;gBACjD,cAAc;YAChB;QACF;QAEA,IAAI,CAAC,YAAa,CAAA,OAAO;QAEzB,IAAI,QAAQ,WAAW,OAAO,aAAa;QAE3C,IAAI,gBAAgB,KAAK;YACvB,SAAS,KAAK,UAAA;YACd,YAAY,cAAc;YAC1B,QAAQ;YAER,cAAc;YACd,eAAe;QACjB,OAAA,IAAW,gBAAgB,KAAK;YAC9B,SAAS,KAAK,SAAA;YACd,YAAY,eAAe;YAC3B,QAAQ;YAER,cAAc;YACd,eAAe;QACjB,OAAO;YACL,OAAO;QACT;QAEA,MAAM,aAAa,QAAQ,IAAI,SAAS,YAAY,SAAS;QAE7D,OAAO,cAAc,eAAe;IACtC;IAAA;;GAAA,GAKA,IAAI,cAAc;QAChB,OACE,IAAA,CAAK,OAAA,CAAQ,OAAA,KAAY,SACrB,SAAS,eAAA,GACT,IAAA,CAAK,OAAA,CAAQ,OAAA;IAErB;IAAA;;GAAA,GAKA,IAAI,QAAQ;QACV,IAAI,IAAA,CAAK,OAAA,CAAQ,+BAAA,EAAiC;YAChD,IAAI,IAAA,CAAK,YAAA,EAAc;gBACrB,OAAO,IAAA,CAAK,WAAA,CAAY,WAAA,GAAc,IAAA,CAAK,WAAA,CAAY,WAAA;YACzD,OAAO;gBACL,OAAO,IAAA,CAAK,WAAA,CAAY,YAAA,GAAe,IAAA,CAAK,WAAA,CAAY,YAAA;YAC1D;QACF,OAAO;YACL,OAAO,IAAA,CAAK,UAAA,CAAW,KAAA,CAAM,IAAA,CAAK,YAAA,GAAe,MAAM,GAAG,CAAA;QAC5D;IACF;IAAA;;GAAA,GAKA,IAAI,eAAe;QACjB,OAAO,IAAA,CAAK,OAAA,CAAQ,WAAA,KAAgB;IACtC;IAAA;;GAAA,GAKA,IAAI,eAAe;QAGjB,MAAM,UAAU,IAAA,CAAK,OAAA,CAAQ,OAAA;QAE7B,OAAO,IAAA,CAAK,YAAA,GACP,QAAmB,OAAA,IAAY,QAAwB,UAAA,GACvD,QAAmB,OAAA,IAAY,QAAwB,SAAA;IAC9D;IAAA;;GAAA,GAKA,IAAI,SAAS;QACX,OAAO,IAAA,CAAK,OAAA,CAAQ,QAAA,GAChB,OAAO,IAAA,CAAK,cAAA,EAAgB,IAAA,CAAK,KAAK,IACtC,IAAA,CAAK,cAAA;IACX;IAAA;;GAAA,GAKA,IAAI,WAAW;QAEb,OAAO,IAAA,CAAK,KAAA,KAAU,IAAI,IAAI,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,KAAA;IACnD;IAAA;;GAAA,GAKA,IAAI,cAAc;QAChB,OAAO,IAAA,CAAK,YAAA;IACd;IAEA,IAAY,YAAY,KAAA,EAAkB;QACxC,IAAI,IAAA,CAAK,YAAA,KAAiB,OAAO;YAC/B,IAAA,CAAK,YAAA,GAAe;YACpB,IAAA,CAAK,eAAA,CAAgB;QACvB;IACF;IAAA;;GAAA,GAKA,IAAI,YAAY;QACd,OAAO,IAAA,CAAK,UAAA;IACd;IAEA,IAAY,UAAU,KAAA,EAAgB;QACpC,IAAI,IAAA,CAAK,UAAA,KAAe,OAAO;YAC7B,IAAA,CAAK,UAAA,GAAa;YAClB,IAAA,CAAK,eAAA,CAAgB;QACvB;IACF;IAAA;;GAAA,GAKA,IAAI,WAAW;QACb,OAAO,IAAA,CAAK,SAAA;IACd;IAEA,IAAY,SAAS,KAAA,EAAgB;QACnC,IAAI,IAAA,CAAK,SAAA,KAAc,OAAO;YAC5B,IAAA,CAAK,SAAA,GAAY;YACjB,IAAA,CAAK,eAAA,CAAgB;QACvB;IACF;IAAA;;GAAA,GAKA,IAAI,WAAW;QACb,OAAO,IAAA,CAAK,WAAA,KAAgB;IAC9B;IAAA;;GAAA,GAKA,IAAI,YAAY;QACd,IAAI,YAAY;QAChB,IAAI,IAAA,CAAK,OAAA,CAAQ,UAAA,CAAY,CAAA,aAAa;QAC1C,IAAI,IAAA,CAAK,SAAA,CAAW,CAAA,aAAa;QACjC,IAAI,IAAA,CAAK,QAAA,CAAU,CAAA,aAAa;QAChC,IAAI,IAAA,CAAK,WAAA,CAAa,CAAA,aAAa;QACnC,IAAI,IAAA,CAAK,WAAA,KAAgB,SAAU,CAAA,aAAa;QAChD,OAAO;IACT;IAEQ,kBAAkB;QACxB,IAAA,CAAK,gBAAA,CAAiB;QAEtB,IAAA,CAAK,WAAA,CAAY,SAAA,GACf,GAAG,IAAA,CAAK,WAAA,CAAY,SAAS,CAAA,CAAA,EAAI,IAAA,CAAK,SAAS,EAAA,CAAG,IAAA,CAAK;IAC3D;IAEQ,mBAAmB;QACzB,IAAA,CAAK,WAAA,CAAY,SAAA,GAAY,IAAA,CAAK,WAAA,CAAY,SAAA,CAC3C,OAAA,CAAQ,iBAAiB,EAAE,EAC3B,IAAA,CAAK;IACV;AACF", "debugId": null}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/is-object.mjs"], "sourcesContent": ["function isObject(value) {\n    return typeof value === \"object\" && value !== null;\n}\n\nexport { isObject };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,SAAS,KAAK;IACnB,OAAO,OAAO,UAAU,YAAY,UAAU;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1020, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/clamp.mjs"], "sourcesContent": ["const clamp = (min, max, v) => {\n    if (v > max)\n        return max;\n    if (v < min)\n        return min;\n    return v;\n};\n\nexport { clamp };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,QAAQ,CAAC,KAAK,KAAK;IACrB,IAAI,IAAI,KACJ,OAAO;IACX,IAAI,IAAI,KACJ,OAAO;IACX,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/format-error-message.mjs"], "sourcesContent": ["function formatErrorMessage(message, errorCode) {\n    return errorCode\n        ? `${message}. For more information and steps for solving, visit https://motion.dev/troubleshooting/${errorCode}`\n        : message;\n}\n\nexport { formatErrorMessage };\n"], "names": [], "mappings": ";;;;AAAA,SAAS,mBAAmB,OAAO,EAAE,SAAS;IAC1C,OAAO,YACD,GAAG,QAAQ,uFAAuF,EAAE,WAAW,GAC/G;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1046, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/errors.mjs"], "sourcesContent": ["import { formatErrorMessage } from './format-error-message.mjs';\n\nlet warning = () => { };\nlet invariant = () => { };\nif (process.env.NODE_ENV !== \"production\") {\n    warning = (check, message, errorCode) => {\n        if (!check && typeof console !== \"undefined\") {\n            console.warn(formatErrorMessage(message, errorCode));\n        }\n    };\n    invariant = (check, message, errorCode) => {\n        if (!check) {\n            throw new Error(formatErrorMessage(message, errorCode));\n        }\n    };\n}\n\nexport { invariant, warning };\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,IAAI,UAAU,KAAQ;AACtB,IAAI,YAAY,KAAQ;AACxB,wCAA2C;IACvC,UAAU,CAAC,OAAO,SAAS;QACvB,IAAI,CAAC,SAAS,OAAO,YAAY,aAAa;YAC1C,QAAQ,IAAI,CAAC,IAAA,gQAAkB,EAAC,SAAS;QAC7C;IACJ;IACA,YAAY,CAAC,OAAO,SAAS;QACzB,IAAI,CAAC,OAAO;YACR,MAAM,IAAI,MAAM,IAAA,gQAAkB,EAAC,SAAS;QAChD;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1073, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/is-numerical-string.mjs"], "sourcesContent": ["/**\n * Check if value is a numerical string, ie a string that is purely a number eg \"100\" or \"-100.1\"\n */\nconst isNumericalString = (v) => /^-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)$/u.test(v);\n\nexport { isNumericalString };\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AACD,MAAM,oBAAoB,CAAC,IAAM,+BAA+B,IAAI,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1085, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/noop.mjs"], "sourcesContent": ["/*#__NO_SIDE_EFFECTS__*/\nconst noop = (any) => any;\n\nexport { noop };\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AACtB,MAAM,OAAO,CAAC,MAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1095, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/global-config.mjs"], "sourcesContent": ["const MotionGlobalConfig = {};\n\nexport { MotionGlobalConfig };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,qBAAqB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/is-zero-value-string.mjs"], "sourcesContent": ["/**\n * Check if the value is a zero value string like \"0px\" or \"0%\"\n */\nconst isZeroValueString = (v) => /^0[^.\\s]+$/u.test(v);\n\nexport { isZeroValueString };\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AACD,MAAM,oBAAoB,CAAC,IAAM,cAAc,IAAI,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/warn-once.mjs"], "sourcesContent": ["import { formatErrorMessage } from './format-error-message.mjs';\n\nconst warned = new Set();\nfunction hasWarned(message) {\n    return warned.has(message);\n}\nfunction warnOnce(condition, message, errorCode) {\n    if (condition || warned.has(message))\n        return;\n    console.warn(formatErrorMessage(message, errorCode));\n    warned.add(message);\n}\n\nexport { hasWarned, warnOnce };\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEA,MAAM,SAAS,IAAI;AACnB,SAAS,UAAU,OAAO;IACtB,OAAO,OAAO,GAAG,CAAC;AACtB;AACA,SAAS,SAAS,SAAS,EAAE,OAAO,EAAE,SAAS;IAC3C,IAAI,aAAa,OAAO,GAAG,CAAC,UACxB;IACJ,QAAQ,IAAI,CAAC,IAAA,gQAAkB,EAAC,SAAS;IACzC,OAAO,GAAG,CAAC;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1139, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/array.mjs"], "sourcesContent": ["function addUniqueItem(arr, item) {\n    if (arr.indexOf(item) === -1)\n        arr.push(item);\n}\nfunction removeItem(arr, item) {\n    const index = arr.indexOf(item);\n    if (index > -1)\n        arr.splice(index, 1);\n}\n// Adapted from array-move\nfunction moveItem([...arr], fromIndex, toIndex) {\n    const startIndex = fromIndex < 0 ? arr.length + fromIndex : fromIndex;\n    if (startIndex >= 0 && startIndex < arr.length) {\n        const endIndex = toIndex < 0 ? arr.length + toIndex : toIndex;\n        const [item] = arr.splice(fromIndex, 1);\n        arr.splice(endIndex, 0, item);\n    }\n    return arr;\n}\n\nexport { addUniqueItem, moveItem, removeItem };\n"], "names": [], "mappings": ";;;;;;;;AAAA,SAAS,cAAc,GAAG,EAAE,IAAI;IAC5B,IAAI,IAAI,OAAO,CAAC,UAAU,CAAC,GACvB,IAAI,IAAI,CAAC;AACjB;AACA,SAAS,WAAW,GAAG,EAAE,IAAI;IACzB,MAAM,QAAQ,IAAI,OAAO,CAAC;IAC1B,IAAI,QAAQ,CAAC,GACT,IAAI,MAAM,CAAC,OAAO;AAC1B;AACA,0BAA0B;AAC1B,SAAS,SAAS,CAAC,GAAG,IAAI,EAAE,SAAS,EAAE,OAAO;IAC1C,MAAM,aAAa,YAAY,IAAI,IAAI,MAAM,GAAG,YAAY;IAC5D,IAAI,cAAc,KAAK,aAAa,IAAI,MAAM,EAAE;QAC5C,MAAM,WAAW,UAAU,IAAI,IAAI,MAAM,GAAG,UAAU;QACtD,MAAM,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,WAAW;QACrC,IAAI,MAAM,CAAC,UAAU,GAAG;IAC5B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/subscription-manager.mjs"], "sourcesContent": ["import { addUniqueItem, removeItem } from './array.mjs';\n\nclass SubscriptionManager {\n    constructor() {\n        this.subscriptions = [];\n    }\n    add(handler) {\n        addUniqueItem(this.subscriptions, handler);\n        return () => removeItem(this.subscriptions, handler);\n    }\n    notify(a, b, c) {\n        const numSubscriptions = this.subscriptions.length;\n        if (!numSubscriptions)\n            return;\n        if (numSubscriptions === 1) {\n            /**\n             * If there's only a single handler we can just call it without invoking a loop.\n             */\n            this.subscriptions[0](a, b, c);\n        }\n        else {\n            for (let i = 0; i < numSubscriptions; i++) {\n                /**\n                 * Check whether the handler exists before firing as it's possible\n                 * the subscriptions were modified during this loop running.\n                 */\n                const handler = this.subscriptions[i];\n                handler && handler(a, b, c);\n            }\n        }\n    }\n    getSize() {\n        return this.subscriptions.length;\n    }\n    clear() {\n        this.subscriptions.length = 0;\n    }\n}\n\nexport { SubscriptionManager };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM;IACF,aAAc;QACV,IAAI,CAAC,aAAa,GAAG,EAAE;IAC3B;IACA,IAAI,OAAO,EAAE;QACT,IAAA,sOAAa,EAAC,IAAI,CAAC,aAAa,EAAE;QAClC,OAAO,IAAM,IAAA,mOAAU,EAAC,IAAI,CAAC,aAAa,EAAE;IAChD;IACA,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACZ,MAAM,mBAAmB,IAAI,CAAC,aAAa,CAAC,MAAM;QAClD,IAAI,CAAC,kBACD;QACJ,IAAI,qBAAqB,GAAG;YACxB;;aAEC,GACD,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,GAAG,GAAG;QAChC,OACK;YACD,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,IAAK;gBACvC;;;iBAGC,GACD,MAAM,UAAU,IAAI,CAAC,aAAa,CAAC,EAAE;gBACrC,WAAW,QAAQ,GAAG,GAAG;YAC7B;QACJ;IACJ;IACA,UAAU;QACN,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM;IACpC;IACA,QAAQ;QACJ,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG;IAChC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1212, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/velocity-per-second.mjs"], "sourcesContent": ["/*\n  Convert velocity into velocity per second\n\n  @param [number]: Unit per frame\n  @param [number]: Frame duration in ms\n*/\nfunction velocityPerSecond(velocity, frameDuration) {\n    return frameDuration ? velocity * (1000 / frameDuration) : 0;\n}\n\nexport { velocityPerSecond };\n"], "names": [], "mappings": "AAAA;;;;;AAKA;;;;AACA,SAAS,kBAAkB,QAAQ,EAAE,aAAa;IAC9C,OAAO,gBAAgB,WAAW,CAAC,OAAO,aAAa,IAAI;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/pipe.mjs"], "sourcesContent": ["/**\n * <PERSON><PERSON>\n * Compose other transformers to run linearily\n * pipe(min(20), max(40))\n * @param  {...functions} transformers\n * @return {function}\n */\nconst combineFunctions = (a, b) => (v) => b(a(v));\nconst pipe = (...transformers) => transformers.reduce(combineFunctions);\n\nexport { pipe };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;AACD,MAAM,mBAAmB,CAAC,GAAG,IAAM,CAAC,IAAM,EAAE,EAAE;AAC9C,MAAM,OAAO,CAAC,GAAG,eAAiB,aAAa,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1246, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/time-conversion.mjs"], "sourcesContent": ["/**\n * Converts seconds to milliseconds\n *\n * @param seconds - Time in seconds.\n * @return milliseconds - Converted time in milliseconds.\n */\n/*#__NO_SIDE_EFFECTS__*/\nconst secondsToMilliseconds = (seconds) => seconds * 1000;\n/*#__NO_SIDE_EFFECTS__*/\nconst millisecondsToSeconds = (milliseconds) => milliseconds / 1000;\n\nexport { millisecondsToSeconds, secondsToMilliseconds };\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GACD,sBAAsB;;;;;;AACtB,MAAM,wBAAwB,CAAC,UAAY,UAAU;AACrD,sBAAsB,GACtB,MAAM,wBAAwB,CAAC,eAAiB,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1264, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/easing/cubic-bezier.mjs"], "sourcesContent": ["import { noop } from '../noop.mjs';\n\n/*\n  Bezier function generator\n  This has been modified from Gaë<PERSON>eau's BezierEasing\n  https://github.com/gre/bezier-easing/blob/master/src/index.js\n  https://github.com/gre/bezier-easing/blob/master/LICENSE\n  \n  I've removed the newtonRaphsonIterate algo because in benchmarking it\n  wasn't noticeably faster than binarySubdivision, indeed removing it\n  usually improved times, depending on the curve.\n  I also removed the lookup table, as for the added bundle size and loop we're\n  only cutting ~4 or so subdivision iterations. I bumped the max iterations up\n  to 12 to compensate and this still tended to be faster for no perceivable\n  loss in accuracy.\n  Usage\n    const easeOut = cubicBezier(.17,.67,.83,.67);\n    const x = easeOut(0.5); // returns 0.627...\n*/\n// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\nconst calcBezier = (t, a1, a2) => (((1.0 - 3.0 * a2 + 3.0 * a1) * t + (3.0 * a2 - 6.0 * a1)) * t + 3.0 * a1) *\n    t;\nconst subdivisionPrecision = 0.0000001;\nconst subdivisionMaxIterations = 12;\nfunction binarySubdivide(x, lowerBound, upperBound, mX1, mX2) {\n    let currentX;\n    let currentT;\n    let i = 0;\n    do {\n        currentT = lowerBound + (upperBound - lowerBound) / 2.0;\n        currentX = calcBezier(currentT, mX1, mX2) - x;\n        if (currentX > 0.0) {\n            upperBound = currentT;\n        }\n        else {\n            lowerBound = currentT;\n        }\n    } while (Math.abs(currentX) > subdivisionPrecision &&\n        ++i < subdivisionMaxIterations);\n    return currentT;\n}\nfunction cubicBezier(mX1, mY1, mX2, mY2) {\n    // If this is a linear gradient, return linear easing\n    if (mX1 === mY1 && mX2 === mY2)\n        return noop;\n    const getTForX = (aX) => binarySubdivide(aX, 0, 1, mX1, mX2);\n    // If animation is at start/end, return t without easing\n    return (t) => t === 0 || t === 1 ? t : calcBezier(getTForX(t), mY1, mY2);\n}\n\nexport { cubicBezier };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA;;;;;;;;;;;;;;;;AAgBA,GACA,iEAAiE;AACjE,MAAM,aAAa,CAAC,GAAG,IAAI,KAAO,CAAC,CAAC,CAAC,MAAM,MAAM,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC,IAAI,IAAI,MAAM,EAAE,IACvG;AACJ,MAAM,uBAAuB;AAC7B,MAAM,2BAA2B;AACjC,SAAS,gBAAgB,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG;IACxD,IAAI;IACJ,IAAI;IACJ,IAAI,IAAI;IACR,GAAG;QACC,WAAW,aAAa,CAAC,aAAa,UAAU,IAAI;QACpD,WAAW,WAAW,UAAU,KAAK,OAAO;QAC5C,IAAI,WAAW,KAAK;YAChB,aAAa;QACjB,OACK;YACD,aAAa;QACjB;IACJ,QAAS,KAAK,GAAG,CAAC,YAAY,wBAC1B,EAAE,IAAI,yBAA0B;IACpC,OAAO;AACX;AACA,SAAS,YAAY,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACnC,qDAAqD;IACrD,IAAI,QAAQ,OAAO,QAAQ,KACvB,OAAO,4NAAI;IACf,MAAM,WAAW,CAAC,KAAO,gBAAgB,IAAI,GAAG,GAAG,KAAK;IACxD,wDAAwD;IACxD,OAAO,CAAC,IAAM,MAAM,KAAK,MAAM,IAAI,IAAI,WAAW,SAAS,IAAI,KAAK;AACxE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1317, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/easing/ease.mjs"], "sourcesContent": ["import { cubicBezier } from './cubic-bezier.mjs';\n\nconst easeIn = /*@__PURE__*/ cubicBezier(0.42, 0, 1, 1);\nconst easeOut = /*@__PURE__*/ cubicBezier(0, 0, 0.58, 1);\nconst easeInOut = /*@__PURE__*/ cubicBezier(0.42, 0, 0.58, 1);\n\nexport { easeIn, easeInOut, easeOut };\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEA,MAAM,SAAS,WAAW,GAAG,IAAA,wPAAW,EAAC,MAAM,GAAG,GAAG;AACrD,MAAM,UAAU,WAAW,GAAG,IAAA,wPAAW,EAAC,GAAG,GAAG,MAAM;AACtD,MAAM,YAAY,WAAW,GAAG,IAAA,wPAAW,EAAC,MAAM,GAAG,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/easing/utils/is-easing-array.mjs"], "sourcesContent": ["const isEasingArray = (ease) => {\n    return Array.isArray(ease) && typeof ease[0] !== \"number\";\n};\n\nexport { isEasingArray };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,gBAAgB,CAAC;IACnB,OAAO,MAAM,OAAO,CAAC,SAAS,OAAO,IAAI,CAAC,EAAE,KAAK;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1347, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/easing/modifiers/mirror.mjs"], "sourcesContent": ["// Accepts an easing function and returns a new one that outputs mirrored values for\n// the second half of the animation. Turns easeIn into easeInOut.\nconst mirrorEasing = (easing) => (p) => p <= 0.5 ? easing(2 * p) / 2 : (2 - easing(2 * (1 - p))) / 2;\n\nexport { mirrorEasing };\n"], "names": [], "mappings": "AAAA,oFAAoF;AACpF,iEAAiE;;;;;AACjE,MAAM,eAAe,CAAC,SAAW,CAAC,IAAM,KAAK,MAAM,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/easing/modifiers/reverse.mjs"], "sourcesContent": ["// Accepts an easing function and returns a new one that outputs reversed values.\n// Turns easeIn into easeOut.\nconst reverseEasing = (easing) => (p) => 1 - easing(1 - p);\n\nexport { reverseEasing };\n"], "names": [], "mappings": "AAAA,iFAAiF;AACjF,6BAA6B;;;;;AAC7B,MAAM,gBAAgB,CAAC,SAAW,CAAC,IAAM,IAAI,OAAO,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/easing/back.mjs"], "sourcesContent": ["import { cubicBezier } from './cubic-bezier.mjs';\nimport { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\n\nconst backOut = /*@__PURE__*/ cubicBezier(0.33, 1.53, 0.69, 0.99);\nconst backIn = /*@__PURE__*/ reverseEasing(backOut);\nconst backInOut = /*@__PURE__*/ mirrorEasing(backIn);\n\nexport { backIn, backInOut, backOut };\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,UAAU,WAAW,GAAG,IAAA,wPAAW,EAAC,MAAM,MAAM,MAAM;AAC5D,MAAM,SAAS,WAAW,GAAG,IAAA,+PAAa,EAAC;AAC3C,MAAM,YAAY,WAAW,GAAG,IAAA,6PAAY,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/easing/anticipate.mjs"], "sourcesContent": ["import { backIn } from './back.mjs';\n\nconst anticipate = (p) => (p *= 2) < 1 ? 0.5 * backIn(p) : 0.5 * (2 - Math.pow(2, -10 * (p - 1)));\n\nexport { anticipate };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,aAAa,CAAC,IAAM,CAAC,KAAK,CAAC,IAAI,IAAI,MAAM,IAAA,wOAAM,EAAC,KAAK,MAAM,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/easing/circ.mjs"], "sourcesContent": ["import { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\n\nconst circIn = (p) => 1 - Math.sin(Math.acos(p));\nconst circOut = reverseEasing(circIn);\nconst circInOut = mirrorEasing(circIn);\n\nexport { circIn, circInOut, circOut };\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEA,MAAM,SAAS,CAAC,IAAM,IAAI,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC;AAC7C,MAAM,UAAU,IAAA,+PAAa,EAAC;AAC9B,MAAM,YAAY,IAAA,6PAAY,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1425, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/easing/utils/is-bezier-definition.mjs"], "sourcesContent": ["const isBezierDefinition = (easing) => Array.isArray(easing) && typeof easing[0] === \"number\";\n\nexport { isBezierDefinition };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,qBAAqB,CAAC,SAAW,MAAM,OAAO,CAAC,WAAW,OAAO,MAAM,CAAC,EAAE,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1435, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/easing/utils/map.mjs"], "sourcesContent": ["import { invariant } from '../../errors.mjs';\nimport { noop } from '../../noop.mjs';\nimport { anticipate } from '../anticipate.mjs';\nimport { backIn, backInOut, backOut } from '../back.mjs';\nimport { circIn, circInOut, circOut } from '../circ.mjs';\nimport { cubicBezier } from '../cubic-bezier.mjs';\nimport { easeIn, easeInOut, easeOut } from '../ease.mjs';\nimport { isBezierDefinition } from './is-bezier-definition.mjs';\n\nconst easingLookup = {\n    linear: noop,\n    easeIn,\n    easeInOut,\n    easeOut,\n    circIn,\n    circInOut,\n    circOut,\n    backIn,\n    backInOut,\n    backOut,\n    anticipate,\n};\nconst isValidEasing = (easing) => {\n    return typeof easing === \"string\";\n};\nconst easingDefinitionToFunction = (definition) => {\n    if (isBezierDefinition(definition)) {\n        // If cubic bezier definition, create bezier curve\n        invariant(definition.length === 4, `Cubic bezier arrays must contain four numerical values.`, \"cubic-bezier-length\");\n        const [x1, y1, x2, y2] = definition;\n        return cubicBezier(x1, y1, x2, y2);\n    }\n    else if (isValidEasing(definition)) {\n        // Else lookup from table\n        invariant(easingLookup[definition] !== undefined, `Invalid easing type '${definition}'`, \"invalid-easing-type\");\n        return easingLookup[definition];\n    }\n    return definition;\n};\n\nexport { easingDefinitionToFunction };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,MAAM,eAAe;IACjB,QAAQ,4NAAI;IACZ,QAAA,wOAAM;IACN,WAAA,2OAAS;IACT,SAAA,yOAAO;IACP,QAAA,wOAAM;IACN,WAAA,2OAAS;IACT,SAAA,yOAAO;IACP,QAAA,wOAAM;IACN,WAAA,2OAAS;IACT,SAAA,yOAAO;IACP,YAAA,kPAAU;AACd;AACA,MAAM,gBAAgB,CAAC;IACnB,OAAO,OAAO,WAAW;AAC7B;AACA,MAAM,6BAA6B,CAAC;IAChC,IAAI,IAAA,mRAAkB,EAAC,aAAa;QAChC,kDAAkD;QAClD,IAAA,mOAAS,EAAC,WAAW,MAAM,KAAK,GAAG,CAAC,uDAAuD,CAAC,EAAE;QAC9F,MAAM,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG;QACzB,OAAO,IAAA,wPAAW,EAAC,IAAI,IAAI,IAAI;IACnC,OACK,IAAI,cAAc,aAAa;QAChC,yBAAyB;QACzB,IAAA,mOAAS,EAAC,YAAY,CAAC,WAAW,KAAK,WAAW,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC,EAAE;QACzF,OAAO,YAAY,CAAC,WAAW;IACnC;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1489, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/progress.mjs"], "sourcesContent": ["/*\n  Progress within given range\n\n  Given a lower limit and an upper limit, we return the progress\n  (expressed as a number 0-1) represented by the given value, and\n  limit that progress to within 0-1.\n\n  @param [number]: Lower limit\n  @param [number]: Upper limit\n  @param [number]: Value to find progress within given range\n  @return [number]: Progress of value within range as expressed 0-1\n*/\n/*#__NO_SIDE_EFFECTS__*/\nconst progress = (from, to, value) => {\n    const toFromDifference = to - from;\n    return toFromDifference === 0 ? 1 : (value - from) / toFromDifference;\n};\n\nexport { progress };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;AAWA,GACA,sBAAsB;;;;AACtB,MAAM,WAAW,CAAC,MAAM,IAAI;IACxB,MAAM,mBAAmB,KAAK;IAC9B,OAAO,qBAAqB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1513, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/motion-utils%4012.23.6/node_modules/motion-utils/dist/es/memo.mjs"], "sourcesContent": ["/*#__NO_SIDE_EFFECTS__*/\nfunction memo(callback) {\n    let result;\n    return () => {\n        if (result === undefined)\n            result = callback();\n        return result;\n    };\n}\n\nexport { memo };\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AACtB,SAAS,KAAK,QAAQ;IAClB,IAAI;IACJ,OAAO;QACH,IAAI,WAAW,WACX,SAAS;QACb,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1529, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/cjs/_interop_require_default.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _interop_require_default(obj) {\n    return obj && obj.__esModule ? obj : { default: obj };\n}\nexports._ = _interop_require_default;\n"], "names": [], "mappings": "AAEA,SAAS,yBAAyB,GAAG;IACjC,OAAO,OAAO,IAAI,UAAU,GAAG,MAAM;QAAE,SAAS;IAAI;AACxD;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1539, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/%40swc%2Bhelpers%400.5.15/node_modules/%40swc/helpers/cjs/_interop_require_wildcard.cjs"], "sourcesContent": ["\"use strict\";\n\nfunction _getRequireWildcardCache(nodeInterop) {\n    if (typeof WeakMap !== \"function\") return null;\n\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop);\n}\nfunction _interop_require_wildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) return obj;\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") return { default: obj };\n\n    var cache = _getRequireWildcardCache(nodeInterop);\n\n    if (cache && cache.has(obj)) return cache.get(obj);\n\n    var newObj = { __proto__: null };\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n\n    for (var key in obj) {\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) Object.defineProperty(newObj, key, desc);\n            else newObj[key] = obj[key];\n        }\n    }\n\n    newObj.default = obj;\n\n    if (cache) cache.set(obj, newObj);\n\n    return newObj;\n}\nexports._ = _interop_require_wildcard;\n"], "names": [], "mappings": "AAEA,SAAS,yBAAyB,WAAW;IACzC,IAAI,OAAO,YAAY,YAAY,OAAO;IAE1C,IAAI,oBAAoB,IAAI;IAC5B,IAAI,mBAAmB,IAAI;IAE3B,OAAO,CAAC,2BAA2B,SAAS,WAAW;QACnD,OAAO,cAAc,mBAAmB;IAC5C,CAAC,EAAE;AACP;AACA,SAAS,0BAA0B,GAAG,EAAE,WAAW;IAC/C,IAAI,CAAC,eAAe,OAAO,IAAI,UAAU,EAAE,OAAO;IAClD,IAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY,OAAO;QAAE,SAAS;IAAI;IAEhG,IAAI,QAAQ,yBAAyB;IAErC,IAAI,SAAS,MAAM,GAAG,CAAC,MAAM,OAAO,MAAM,GAAG,CAAC;IAE9C,IAAI,SAAS;QAAE,WAAW;IAAK;IAC/B,IAAI,wBAAwB,OAAO,cAAc,IAAI,OAAO,wBAAwB;IAEpF,IAAK,IAAI,OAAO,IAAK;QACjB,IAAI,QAAQ,aAAa,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;YACrE,IAAI,OAAO,wBAAwB,OAAO,wBAAwB,CAAC,KAAK,OAAO;YAC/E,IAAI,QAAQ,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG,OAAO,cAAc,CAAC,QAAQ,KAAK;iBAClE,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QAC/B;IACJ;IAEA,OAAO,OAAO,GAAG;IAEjB,IAAI,OAAO,MAAM,GAAG,CAAC,KAAK;IAE1B,OAAO;AACX;AACA,QAAQ,CAAC,GAAG", "ignoreList": [0], "debugId": null}}]}