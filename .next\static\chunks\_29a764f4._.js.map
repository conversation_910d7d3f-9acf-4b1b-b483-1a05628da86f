{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/components/custom/preloader/anim.ts"], "sourcesContent": ["export const opacity = {\r\n  initial: {\r\n    opacity: 0,\r\n  },\r\n  enter: {\r\n    opacity: 0.75,\r\n    transition: { duration: 1, delay: 0.2 },\r\n  },\r\n};\r\n\r\nexport const slideUp = {\r\n  initial: {\r\n    top: 0,\r\n  },\r\n  exit: {\r\n    top: \"-100vh\",\r\n    transition: {\r\n      duration: 0.8,\r\n      ease: [0.76, 0, 0.24, 1] as const,\r\n      delay: 0.2,\r\n    },\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAO,MAAM,UAAU;IACrB,SAAS;QACP,SAAS;IACX;IACA,OAAO;QACL,SAAS;QACT,YAAY;YAAE,UAAU;YAAG,OAAO;QAAI;IACxC;AACF;AAEO,MAAM,UAAU;IACrB,SAAS;QACP,KAAK;IACP;IACA,MAAM;QACJ,KAAK;QACL,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAM;gBAAG;gBAAM;aAAE;YACxB,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/components/custom/preloader/index.tsx"], "sourcesContent": ["import { motion } from \"motion/react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { opacity, slideUp } from \"./anim\";\r\n\r\n// import { opacity, slideUp } from \"./anim\";\r\n// import styles from \"./style.module.scss\";\r\n\r\nconst words = [\r\n  \"Hello\",\r\n  \"<PERSON><PERSON><PERSON>\",\r\n  \"<PERSON>ia<PERSON>\",\r\n  \"<PERSON><PERSON><PERSON>\",\r\n  \"やあ\",\r\n  \"Hallå\",\r\n  \"Guten tag\",\r\n  \"Hallo\",\r\n  \"Amakuru\",\r\n];\r\n\r\nexport default function Preloader() {\r\n  const [index, setIndex] = useState(0);\r\n  const [dimension, setDimension] = useState({ width: 0, height: 0 });\r\n\r\n  useEffect(() => {\r\n    setDimension({ width: window.innerWidth, height: window.innerHeight });\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (index === words.length - 1) return;\r\n    setTimeout(\r\n      () => {\r\n        setIndex(index + 1);\r\n      },\r\n      index === 0 ? 1000 : 150,\r\n    );\r\n  }, [index]);\r\n\r\n  const initialPath = `M0 0 L${dimension.width} 0 L${dimension.width} ${dimension.height} Q${dimension.width / 2} ${dimension.height + 300} 0 ${dimension.height}  L0 0`;\r\n  const targetPath = `M0 0 L${dimension.width} 0 L${dimension.width} ${dimension.height} Q${dimension.width / 2} ${dimension.height} 0 ${dimension.height}  L0 0`;\r\n\r\n  const curve = {\r\n    initial: {\r\n      d: initialPath,\r\n      transition: { duration: 0.7, ease: [0.76, 0, 0.24, 1] as const },\r\n    },\r\n    exit: {\r\n      d: targetPath,\r\n      transition: {\r\n        duration: 0.7,\r\n        ease: [0.76, 0, 0.24, 1] as const,\r\n        delay: 0.3,\r\n      },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      variants={slideUp}\r\n      initial=\"initial\"\r\n      exit=\"exit\"\r\n      className=\"h-screen w-screen fixed flex bg-white items-center justify-center  z-99\"\r\n    >\r\n      {dimension.width > 0 && (\r\n        <>\r\n          <motion.p\r\n            variants={opacity}\r\n            initial=\"initial\"\r\n            animate=\"enter\"\r\n            className=\"flex text-black items-center absolute z-10 text-5xl lg:text-7xl 2xl:text-9xl\"\r\n          >\r\n            <span className=\"block size-5 bg-black rounded-full mr-2.5\"></span>\r\n            {words[index]}\r\n          </motion.p>\r\n          <svg className=\"absolute top-0 w-full h-[calc(100%+300px)]\">\r\n            <title>Wave</title>\r\n            <motion.path\r\n              variants={curve}\r\n              initial=\"initial\"\r\n              exit=\"exit\"\r\n              className=\"fill-white\"\r\n            ></motion.path>\r\n          </svg>\r\n        </>\r\n      )}\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;;AAEA,6CAA6C;AAC7C,4CAA4C;AAE5C,MAAM,QAAQ;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,wSAAQ,EAAC;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,wSAAQ,EAAC;QAAE,OAAO;QAAG,QAAQ;IAAE;IAEjE,IAAA,ySAAS;+BAAC;YACR,aAAa;gBAAE,OAAO,OAAO,UAAU;gBAAE,QAAQ,OAAO,WAAW;YAAC;QACtE;8BAAG,EAAE;IAEL,IAAA,ySAAS;+BAAC;YACR,IAAI,UAAU,MAAM,MAAM,GAAG,GAAG;YAChC;uCACE;oBACE,SAAS,QAAQ;gBACnB;sCACA,UAAU,IAAI,OAAO;QAEzB;8BAAG;QAAC;KAAM;IAEV,MAAM,cAAc,AAAC,SAA8B,OAAtB,UAAU,KAAK,EAAC,QAAyB,OAAnB,UAAU,KAAK,EAAC,KAAwB,OAArB,UAAU,MAAM,EAAC,MAA2B,OAAvB,UAAU,KAAK,GAAG,GAAE,KAA+B,OAA5B,UAAU,MAAM,GAAG,KAAI,OAAsB,OAAjB,UAAU,MAAM,EAAC;IAC/J,MAAM,aAAa,AAAC,SAA8B,OAAtB,UAAU,KAAK,EAAC,QAAyB,OAAnB,UAAU,KAAK,EAAC,KAAwB,OAArB,UAAU,MAAM,EAAC,MAA2B,OAAvB,UAAU,KAAK,GAAG,GAAE,KAAyB,OAAtB,UAAU,MAAM,EAAC,OAAsB,OAAjB,UAAU,MAAM,EAAC;IAExJ,MAAM,QAAQ;QACZ,SAAS;YACP,GAAG;YACH,YAAY;gBAAE,UAAU;gBAAK,MAAM;oBAAC;oBAAM;oBAAG;oBAAM;iBAAE;YAAU;QACjE;QACA,MAAM;YACJ,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAM;oBAAG;oBAAM;iBAAE;gBACxB,OAAO;YACT;QACF;IACF;IAEA,qBACE,4TAAC,6SAAM,CAAC,GAAG;QACT,UAAU,uJAAO;QACjB,SAAQ;QACR,MAAK;QACL,WAAU;kBAET,UAAU,KAAK,GAAG,mBACjB;;8BACE,4TAAC,6SAAM,CAAC,CAAC;oBACP,UAAU,uJAAO;oBACjB,SAAQ;oBACR,SAAQ;oBACR,WAAU;;sCAEV,4TAAC;4BAAK,WAAU;;;;;;wBACf,KAAK,CAAC,MAAM;;;;;;;8BAEf,4TAAC;oBAAI,WAAU;;sCACb,4TAAC;sCAAM;;;;;;sCACP,4TAAC,6SAAM,CAAC,IAAI;4BACV,UAAU;4BACV,SAAQ;4BACR,MAAK;4BACL,WAAU;;;;;;;;;;;;;;;;;;;AAOxB;GAnEwB;KAAA", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/public/images/a.jpg.mjs%20%28structured%20image%20object%20with%20data%20url%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 4032, height: 3024, blurWidth: 8, blurHeight: 6, blurDataURL: \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAgAAAQABAAD/wAARCAAGAAgDAREAAhEBAxEB/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwCRJtauD5UF/GpIPzPH1I4NV7eT3RzOg1sz/9k=\"};\n"], "names": [], "mappings": ";;;;AAAA;;uCACe;IAAE,KAAA,yHAAG;IAAE,OAAO;IAAM,QAAQ;IAAM,WAAW;IAAG,YAAY;IAAG,aAAa;AAA63B", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/public/images/d.jpg.mjs%20%28structured%20image%20object%20with%20data%20url%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 6880, height: 5504, blurWidth: 8, blurHeight: 6, blurDataURL: \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAgAAAQABAAD/wAARCAAGAAgDAREAAhEBAxEB/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwClqWi3OosHuLwL8+1Qi54x1Oe9eBRrwgrJdDlVmf/Z\"};\n"], "names": [], "mappings": ";;;;AAAA;;uCACe;IAAE,KAAA,yHAAG;IAAE,OAAO;IAAM,QAAQ;IAAM,WAAW;IAAG,YAAY;IAAG,aAAa;AAAi4B", "debugId": null}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/features/home/<USER>/images.tsx"], "sourcesContent": ["import { motion } from \"motion/react\";\r\nimport Image from \"next/image\";\r\nimport { useEffect, useState } from \"react\";\r\nimport cover from \"@/public/images/a.jpg\";\r\nimport cover2 from \"@/public/images/d.jpg\";\r\n\r\nconst images = [cover, cover2];\r\nexport default function ImagesHero() {\r\n  const [index, setIndex] = useState(0);\r\n\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      setIndex((prev) => (prev + 1) % images.length);\r\n    }, 4000);\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"absolute inset-0\">\r\n      {images.map((img, i) => (\r\n        <motion.div\r\n          key={i}\r\n          initial={{ opacity: 0, scale: 1 }}\r\n          animate={{\r\n            opacity: i === index ? 1 : 0,\r\n            scale: i === index ? 1.2 : 1, // scale active image\r\n          }}\r\n          transition={{\r\n            duration: 1.5,\r\n            ease: \"easeInOut\",\r\n          }}\r\n          className=\"absolute inset-0\"\r\n        >\r\n          <Image src={img} alt=\"bg\" fill className=\"object-cover\" priority />\r\n        </motion.div>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,SAAS;IAAC,ySAAK;IAAE,ySAAM;CAAC;AACf,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,wSAAQ,EAAC;IAEnC,IAAA,ySAAS;gCAAC;YACR,MAAM,WAAW;iDAAY;oBAC3B;yDAAS,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,OAAO,MAAM;;gBAC/C;gDAAG;YACH;wCAAO,IAAM,cAAc;;QAC7B;+BAAG,EAAE;IAEL,qBACE,4TAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,KAAK,kBAChB,4TAAC,6SAAM,CAAC,GAAG;gBAET,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,SAAS;oBACP,SAAS,MAAM,QAAQ,IAAI;oBAC3B,OAAO,MAAM,QAAQ,MAAM;gBAC7B;gBACA,YAAY;oBACV,UAAU;oBACV,MAAM;gBACR;gBACA,WAAU;0BAEV,cAAA,4TAAC,0QAAK;oBAAC,KAAK;oBAAK,KAAI;oBAAK,IAAI;oBAAC,WAAU;oBAAe,QAAQ;;;;;;eAZ3D;;;;;;;;;;AAiBf;GA/BwB;KAAA", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/features/home/<USER>/paragraphs.tsx"], "sourcesContent": ["import gsap from \"gsap\";\r\nimport { ScrollTrigger } from \"gsap/all\";\r\nimport { useCallback, useEffect, useRef } from \"react\";\r\n\r\nexport default function ScrollParagraphHero() {\r\n  const firstText = useRef<HTMLParagraphElement>(null);\r\n  const secondText = useRef<HTMLParagraphElement>(null);\r\n  const slider = useRef<HTMLDivElement>(null);\r\n  const xPercent = useRef(0);\r\n  const direction = useRef(-1);\r\n\r\n  const animate = useCallback(() => {\r\n    if (xPercent.current < -100) {\r\n      xPercent.current = 0;\r\n    } else if (xPercent.current > 0) {\r\n      xPercent.current = -100;\r\n    }\r\n    gsap.set(firstText.current, { xPercent: xPercent.current });\r\n    gsap.set(secondText.current, { xPercent: xPercent.current });\r\n    requestAnimationFrame(animate);\r\n    xPercent.current += 0.08 * direction.current;\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    gsap.registerPlugin(ScrollTrigger);\r\n    gsap.to(slider.current, {\r\n      scrollTrigger: {\r\n        trigger: document.documentElement,\r\n        scrub: 0.25,\r\n        start: 0,\r\n        end: window.innerHeight,\r\n        onUpdate: (e) => {\r\n          direction.current = e.direction * -1;\r\n        },\r\n      },\r\n      x: \"-500px\",\r\n    });\r\n    requestAnimationFrame(animate);\r\n  }, [animate]);\r\n\r\n  return (\r\n    <div className=\"absolute top-[calc(100vh-300px)]\">\r\n      <div ref={slider} className=\"relative whitespace-nowrap\">\r\n        <p\r\n          ref={firstText}\r\n          className=\"relative m-0 font-edu text-white text-7xl lg:text-[230px] pr-[50px] font-medium\"\r\n        >\r\n          Welcome in Rwanda -\r\n        </p>\r\n        <p\r\n          ref={secondText}\r\n          className=\"absolute left-full font-edu top-0 m-0 text-7xl text-white lg:text-[230px] pr-[50px] font-medium\"\r\n        >\r\n          Welcome in Rwanda -\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;;AAEe,SAAS;;IACtB,MAAM,YAAY,IAAA,sSAAM,EAAuB;IAC/C,MAAM,aAAa,IAAA,sSAAM,EAAuB;IAChD,MAAM,SAAS,IAAA,sSAAM,EAAiB;IACtC,MAAM,WAAW,IAAA,sSAAM,EAAC;IACxB,MAAM,YAAY,IAAA,sSAAM,EAAC,CAAC;IAE1B,MAAM,UAAU,IAAA,2SAAW;oDAAC;YAC1B,IAAI,SAAS,OAAO,GAAG,CAAC,KAAK;gBAC3B,SAAS,OAAO,GAAG;YACrB,OAAO,IAAI,SAAS,OAAO,GAAG,GAAG;gBAC/B,SAAS,OAAO,GAAG,CAAC;YACtB;YACA,6MAAI,CAAC,GAAG,CAAC,UAAU,OAAO,EAAE;gBAAE,UAAU,SAAS,OAAO;YAAC;YACzD,6MAAI,CAAC,GAAG,CAAC,WAAW,OAAO,EAAE;gBAAE,UAAU,SAAS,OAAO;YAAC;YAC1D,sBAAsB;YACtB,SAAS,OAAO,IAAI,OAAO,UAAU,OAAO;QAC9C;mDAAG,EAAE;IAEL,IAAA,ySAAS;yCAAC;YACR,6MAAI,CAAC,cAAc,CAAC,2MAAa;YACjC,6MAAI,CAAC,EAAE,CAAC,OAAO,OAAO,EAAE;gBACtB,eAAe;oBACb,SAAS,SAAS,eAAe;oBACjC,OAAO;oBACP,OAAO;oBACP,KAAK,OAAO,WAAW;oBACvB,QAAQ;yDAAE,CAAC;4BACT,UAAU,OAAO,GAAG,EAAE,SAAS,GAAG,CAAC;wBACrC;;gBACF;gBACA,GAAG;YACL;YACA,sBAAsB;QACxB;wCAAG;QAAC;KAAQ;IAEZ,qBACE,4TAAC;QAAI,WAAU;kBACb,cAAA,4TAAC;YAAI,KAAK;YAAQ,WAAU;;8BAC1B,4TAAC;oBACC,KAAK;oBACL,WAAU;8BACX;;;;;;8BAGD,4TAAC;oBACC,KAAK;oBACL,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT;GAtDwB;KAAA", "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/features/home/<USER>/index.tsx"], "sourcesContent": ["\"use client\";\r\nimport { motion, useScroll, useTransform } from \"motion/react\";\r\nimport { useRef } from \"react\";\r\nimport ImagesHero from \"./images\";\r\nimport ScrollParagraphHero from \"./paragraphs\";\r\nexport default function HomeHero() {\r\n  const container = useRef<HTMLDivElement>(null);\r\n  const { scrollYProgress } = useScroll({\r\n    target: container,\r\n    offset: [\"start start\", \"end start\"],\r\n  });\r\n\r\n  const y = useTransform(scrollYProgress, [0, 1], [\"0vh\", \"120vh\"]);\r\n  return (\r\n    <main className=\"bg-white relative flex h-screen overflow-hidden\">\r\n      <motion.div\r\n        style={{ y }}\r\n        className=\"relative h-full w-full\"\r\n        ref={container}\r\n      >\r\n        <div className=\"flex justify-between z-10 opacity-100 px-4 lg:px-6 py-5 \">\r\n          <p className=\"text-2xl text-white font-edu tracking-tighter font-semibold\">\r\n            Rwanda Connect\r\n          </p>\r\n        </div>\r\n        <ImagesHero />\r\n        <ScrollParagraphHero />\r\n      </motion.div>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AAAA;AACA;AACA;AACA;;;AAJA;;;;;AAKe,SAAS;;IACtB,MAAM,YAAY,IAAA,sSAAM,EAAiB;IACzC,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,+RAAS,EAAC;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAe;SAAY;IACtC;IAEA,MAAM,IAAI,IAAA,qSAAY,EAAC,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAO;KAAQ;IAChE,qBACE,4TAAC;QAAK,WAAU;kBACd,cAAA,4TAAC,6SAAM,CAAC,GAAG;YACT,OAAO;gBAAE;YAAE;YACX,WAAU;YACV,KAAK;;8BAEL,4TAAC;oBAAI,WAAU;8BACb,cAAA,4TAAC;wBAAE,WAAU;kCAA8D;;;;;;;;;;;8BAI7E,4TAAC,iJAAU;;;;;8BACX,4TAAC,qJAAmB;;;;;;;;;;;;;;;;AAI5B;GAzBwB;;QAEM,+RAAS;QAK3B,qSAAY;;;KAPA", "debugId": null}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/app/page.tsx"], "sourcesContent": ["\"use client\";\nimport Lenis from \"lenis\";\nimport { AnimatePresence } from \"motion/react\";\nimport { useEffect, useState } from \"react\";\nimport Preloader from \"@/components/custom/preloader\";\nimport AboutHome from \"@/features/home/<USER>\";\nimport HomeHero from \"@/features/home/<USER>\";\n\nexport default function Home() {\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    const lenis = new Lenis();\n\n    function raf(time: number) {\n      lenis.raf(time);\n      requestAnimationFrame(raf);\n    }\n\n    setTimeout(() => {\n      setIsLoading(false);\n      document.body.style.cursor = \"default\";\n      window.scrollTo(0, 0);\n    }, 2000);\n\n    requestAnimationFrame(raf);\n  }, []);\n  return (\n    <>\n      <AnimatePresence mode=\"wait\">\n        {isLoading && <Preloader />}\n      </AnimatePresence>\n      <HomeHero />\n      <AboutHome />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;;;;;;AAEA;;;AANA;;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,wSAAQ,EAAC;IAE3C,IAAA,ySAAS;0BAAC;YACR,MAAM,QAAQ,IAAI,6NAAK;YAEvB,SAAS,IAAI,IAAY;gBACvB,MAAM,GAAG,CAAC;gBACV,sBAAsB;YACxB;YAEA;kCAAW;oBACT,aAAa;oBACb,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;oBAC7B,OAAO,QAAQ,CAAC,GAAG;gBACrB;iCAAG;YAEH,sBAAsB;QACxB;yBAAG,EAAE;IACL,qBACE;;0BACE,4TAAC,qTAAe;gBAAC,MAAK;0BACnB,2BAAa,4TAAC,yJAAS;;;;;;;;;;0BAE1B,4TAAC,gJAAQ;;;;;0BACT,4TAAC;;;;;;;AAGP;GA5BwB;KAAA", "debugId": null}}]}