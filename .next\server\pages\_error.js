var R=require("../chunks/ssr/[turbopack]_runtime.js")("server/pages/_error.js")
R.c("server/chunks/ssr/node_modules__pnpm_21cae76e._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e6a4d965._.js")
R.c("server/chunks/ssr/3d567_next_494d3d2c._.js")
R.c("server/chunks/ssr/node_modules__pnpm_de5056e0._.js")
R.c("server/chunks/ssr/[externals]_next_dist_shared_lib_no-fallback-error_external_59b92b38.js")
R.m("[project]/node_modules/.pnpm/next@15.5.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/node_modules/.pnpm/next@15.5.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/error.js [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/node_modules/.pnpm/next@15.5.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/document.js [ssr] (ecmascript)\", INNER_APP => \"[project]/node_modules/.pnpm/next@15.5.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/app.js [ssr] (ecmascript)\" } [ssr] (ecmascript)")
module.exports=R.m("[project]/node_modules/.pnpm/next@15.5.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/node_modules/.pnpm/next@15.5.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/error.js [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/node_modules/.pnpm/next@15.5.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/document.js [ssr] (ecmascript)\", INNER_APP => \"[project]/node_modules/.pnpm/next@15.5.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/app.js [ssr] (ecmascript)\" } [ssr] (ecmascript)").exports
