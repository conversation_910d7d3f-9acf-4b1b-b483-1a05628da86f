{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack-node]/transforms/transforms.ts"], "sourcesContent": ["/**\r\n * Shared utilities for our 2 transform implementations.\r\n */\r\n\r\nimport type { Ipc } from '../ipc/evaluate'\r\nimport { relative, isAbsolute, join, sep } from 'path'\r\nimport { type StructuredError } from '../ipc'\r\nimport { type StackFrame } from '../compiled/stacktrace-parser'\r\n\r\nexport type IpcInfoMessage =\r\n  | {\r\n      type: 'dependencies'\r\n      envVariables?: string[]\r\n      directories?: Array<[string, string]>\r\n      filePaths?: string[]\r\n      buildFilePaths?: string[]\r\n    }\r\n  | {\r\n      type: 'emittedError'\r\n      severity: 'warning' | 'error'\r\n      error: StructuredError\r\n    }\r\n  | {\r\n      type: 'log'\r\n      logs: Array<{\r\n        time: number\r\n        logType: string\r\n        args: any[]\r\n        trace?: StackFrame[]\r\n      }>\r\n    }\r\n\r\nexport type IpcRequestMessage = {\r\n  type: 'resolve'\r\n  options: any\r\n  lookupPath: string\r\n  request: string\r\n}\r\n\r\nexport type TransformIpc = Ipc<IpcInfoMessage, IpcRequestMessage>\r\n\r\nconst contextDir = process.cwd()\r\nexport const toPath = (file: string) => {\r\n  const relPath = relative(contextDir, file)\r\n  if (isAbsolute(relPath)) {\r\n    throw new Error(\r\n      `Cannot depend on path (${file}) outside of root directory (${contextDir})`\r\n    )\r\n  }\r\n  return sep !== '/' ? relPath.replaceAll(sep, '/') : relPath\r\n}\r\nexport const fromPath = (path: string) => {\r\n  return join(contextDir, sep !== '/' ? path.replaceAll('/', sep) : path)\r\n}\r\n\r\n// Patch process.env to track which env vars are read\r\nconst originalEnv = process.env\r\nconst readEnvVars = new Set<string>()\r\nprocess.env = new Proxy(originalEnv, {\r\n  get(target, prop) {\r\n    if (typeof prop === 'string') {\r\n      // We register the env var as dependency on the\r\n      // current transform and all future transforms\r\n      // since the env var might be cached in module scope\r\n      // and influence them all\r\n      readEnvVars.add(prop)\r\n    }\r\n    return Reflect.get(target, prop)\r\n  },\r\n  set(target, prop, value) {\r\n    return Reflect.set(target, prop, value)\r\n  },\r\n})\r\n\r\nexport function getReadEnvVariables(): string[] {\r\n  return Array.from(readEnvVars)\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;AAGD;;AAoCA,MAAM,aAAa,QAAQ,GAAG;AACvB,MAAM,SAAS,CAAC;IACrB,MAAM,UAAU,IAAA,6GAAQ,EAAC,YAAY;IACrC,IAAI,IAAA,+GAAU,EAAC,UAAU;QACvB,MAAM,IAAI,MACR,CAAC,uBAAuB,EAAE,KAAK,6BAA6B,EAAE,WAAW,CAAC,CAAC;IAE/E;IACA,OAAO,wGAAG,KAAK,MAAM,QAAQ,UAAU,CAAC,wGAAG,EAAE,OAAO;AACtD;AACO,MAAM,WAAW,CAAC;IACvB,OAAO,IAAA,yGAAI,EAAC,YAAY,wGAAG,KAAK,MAAM,KAAK,UAAU,CAAC,KAAK,wGAAG,IAAI;AACpE;AAEA,qDAAqD;AACrD,MAAM,cAAc,QAAQ,GAAG;AAC/B,MAAM,cAAc,IAAI;AACxB,QAAQ,GAAG,GAAG,IAAI,MAAM,aAAa;IACnC,KAAI,MAAM,EAAE,IAAI;QACd,IAAI,OAAO,SAAS,UAAU;YAC5B,+CAA+C;YAC/C,8CAA8C;YAC9C,oDAAoD;YACpD,yBAAyB;YACzB,YAAY,GAAG,CAAC;QAClB;QACA,OAAO,QAAQ,GAAG,CAAC,QAAQ;IAC7B;IACA,KAAI,MAAM,EAAE,IAAI,EAAE,KAAK;QACrB,OAAO,QAAQ,GAAG,CAAC,QAAQ,MAAM;IACnC;AACF;AAEO,SAAS;IACd,OAAO,MAAM,IAAI,CAAC;AACpB"}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack-node]/transforms/webpack-loaders.ts"], "sourcesContent": ["declare const __turbopack_external_require__: {\r\n  resolve: (name: string, opt: { paths: string[] }) => string\r\n} & ((id: string, thunk: () => any, esm?: boolean) => any)\r\n\r\nimport type { Ipc } from '../ipc/evaluate'\r\nimport { dirname, resolve as pathResolve } from 'path'\r\nimport {\r\n  StackFrame,\r\n  parse as parseStackTrace,\r\n} from '../compiled/stacktrace-parser'\r\nimport { structuredError, type StructuredError } from '../ipc'\r\nimport {\r\n  fromPath,\r\n  getReadEnvVariables,\r\n  toPath,\r\n  type TransformIpc,\r\n} from './transforms'\r\n\r\nexport type IpcInfoMessage =\r\n  | {\r\n      type: 'dependencies'\r\n      envVariables?: string[]\r\n      directories?: Array<[string, string]>\r\n      filePaths?: string[]\r\n      buildFilePaths?: string[]\r\n    }\r\n  | {\r\n      type: 'emittedError'\r\n      severity: 'warning' | 'error'\r\n      error: StructuredError\r\n    }\r\n  | {\r\n      type: 'log'\r\n      logs: Array<{\r\n        time: number\r\n        logType: string\r\n        args: any[]\r\n        trace?: StackFrame[]\r\n      }>\r\n    }\r\n\r\nexport type IpcRequestMessage = {\r\n  type: 'resolve'\r\n  options: any\r\n  lookupPath: string\r\n  request: string\r\n}\r\n\r\ntype LoaderConfig =\r\n  | string\r\n  | {\r\n      loader: string\r\n      options: { [k: string]: unknown }\r\n    }\r\n\r\nconst {\r\n  runLoaders,\r\n}: typeof import('loader-runner') = require('@vercel/turbopack/loader-runner')\r\n\r\nconst contextDir = process.cwd()\r\n\r\nconst LogType = Object.freeze({\r\n  error: 'error',\r\n  warn: 'warn',\r\n  info: 'info',\r\n  log: 'log',\r\n  debug: 'debug',\r\n\r\n  trace: 'trace',\r\n\r\n  group: 'group',\r\n  groupCollapsed: 'groupCollapsed',\r\n  groupEnd: 'groupEnd',\r\n\r\n  profile: 'profile',\r\n  profileEnd: 'profileEnd',\r\n\r\n  time: 'time',\r\n\r\n  clear: 'clear',\r\n  status: 'status',\r\n})\r\n\r\nconst loaderFlag = 'LOADER_EXECUTION'\r\n\r\nconst cutOffByFlag = (stack: string, flag: string): string => {\r\n  const errorStack = stack.split('\\n')\r\n  for (let i = 0; i < errorStack.length; i++) {\r\n    if (errorStack[i].includes(flag)) {\r\n      errorStack.length = i\r\n    }\r\n  }\r\n  return errorStack.join('\\n')\r\n}\r\n\r\n/**\r\n * @param stack stack trace\r\n * @returns stack trace without the loader execution flag included\r\n */\r\nconst cutOffLoaderExecution = (stack: string): string =>\r\n  cutOffByFlag(stack, loaderFlag)\r\n\r\nclass DummySpan {\r\n  traceChild() {\r\n    return new DummySpan()\r\n  }\r\n\r\n  traceFn<T>(fn: (span: DummySpan) => T): T {\r\n    return fn(this)\r\n  }\r\n\r\n  async traceAsyncFn<T>(fn: (span: DummySpan) => T | Promise<T>): Promise<T> {\r\n    return await fn(this)\r\n  }\r\n\r\n  stop() {\r\n    return\r\n  }\r\n}\r\n\r\ntype ResolveOptions = {\r\n  dependencyType?: string\r\n  alias?: Record<string, string[]> | unknown[]\r\n  aliasFields?: string[]\r\n  cacheWithContext?: boolean\r\n  conditionNames?: string[]\r\n  descriptionFiles?: string[]\r\n  enforceExtension?: boolean\r\n  extensionAlias: Record<string, string[]>\r\n  extensions?: string[]\r\n  fallback?: Record<string, string[]>\r\n  mainFields?: string[]\r\n  mainFiles?: string[]\r\n  exportsFields?: string[]\r\n  modules?: string[]\r\n  plugins?: unknown[]\r\n  symlinks?: boolean\r\n  unsafeCache?: boolean\r\n  useSyncFileSystemCalls?: boolean\r\n  preferRelative?: boolean\r\n  preferAbsolute?: boolean\r\n  restrictions?: unknown[]\r\n  roots?: string[]\r\n  importFields?: string[]\r\n}\r\n\r\nconst transform = (\r\n  ipc: TransformIpc,\r\n  content: string | { binary: string },\r\n  name: string,\r\n  query: string,\r\n  loaders: LoaderConfig[],\r\n  sourceMap: boolean\r\n) => {\r\n  return new Promise((resolve, reject) => {\r\n    const resource = pathResolve(contextDir, name)\r\n    const resourceDir = dirname(resource)\r\n\r\n    const loadersWithOptions = loaders.map((loader) =>\r\n      typeof loader === 'string' ? { loader, options: {} } : loader\r\n    )\r\n\r\n    const logs: Array<{\r\n      time: number\r\n      logType: string\r\n      args: unknown[]\r\n      trace: StackFrame[] | undefined\r\n    }> = []\r\n\r\n    runLoaders(\r\n      {\r\n        resource: resource + query,\r\n        context: {\r\n          _module: {\r\n            // For debugging purpose, if someone find context is not full compatible to\r\n            // webpack they can guess this comes from turbopack\r\n            __reserved: 'TurbopackContext',\r\n          },\r\n          currentTraceSpan: new DummySpan(),\r\n          rootContext: contextDir,\r\n          sourceMap,\r\n          getOptions() {\r\n            const entry = this.loaders[this.loaderIndex]\r\n            return entry.options && typeof entry.options === 'object'\r\n              ? entry.options\r\n              : {}\r\n          },\r\n          getResolve: (options: ResolveOptions) => {\r\n            const rustOptions = {\r\n              aliasFields: undefined as undefined | string[],\r\n              conditionNames: undefined as undefined | string[],\r\n              noPackageJson: false,\r\n              extensions: undefined as undefined | string[],\r\n              mainFields: undefined as undefined | string[],\r\n              noExportsField: false,\r\n              mainFiles: undefined as undefined | string[],\r\n              noModules: false,\r\n              preferRelative: false,\r\n            }\r\n            if (options.alias) {\r\n              if (!Array.isArray(options.alias) || options.alias.length > 0) {\r\n                throw new Error('alias resolve option is not supported')\r\n              }\r\n            }\r\n            if (options.aliasFields) {\r\n              if (!Array.isArray(options.aliasFields)) {\r\n                throw new Error('aliasFields resolve option must be an array')\r\n              }\r\n              rustOptions.aliasFields = options.aliasFields\r\n            }\r\n            if (options.conditionNames) {\r\n              if (!Array.isArray(options.conditionNames)) {\r\n                throw new Error(\r\n                  'conditionNames resolve option must be an array'\r\n                )\r\n              }\r\n              rustOptions.conditionNames = options.conditionNames\r\n            }\r\n            if (options.descriptionFiles) {\r\n              if (\r\n                !Array.isArray(options.descriptionFiles) ||\r\n                options.descriptionFiles.length > 0\r\n              ) {\r\n                throw new Error(\r\n                  'descriptionFiles resolve option is not supported'\r\n                )\r\n              }\r\n              rustOptions.noPackageJson = true\r\n            }\r\n            if (options.extensions) {\r\n              if (!Array.isArray(options.extensions)) {\r\n                throw new Error('extensions resolve option must be an array')\r\n              }\r\n              rustOptions.extensions = options.extensions\r\n            }\r\n            if (options.mainFields) {\r\n              if (!Array.isArray(options.mainFields)) {\r\n                throw new Error('mainFields resolve option must be an array')\r\n              }\r\n              rustOptions.mainFields = options.mainFields\r\n            }\r\n            if (options.exportsFields) {\r\n              if (\r\n                !Array.isArray(options.exportsFields) ||\r\n                options.exportsFields.length > 0\r\n              ) {\r\n                throw new Error('exportsFields resolve option is not supported')\r\n              }\r\n              rustOptions.noExportsField = true\r\n            }\r\n            if (options.mainFiles) {\r\n              if (!Array.isArray(options.mainFiles)) {\r\n                throw new Error('mainFiles resolve option must be an array')\r\n              }\r\n              rustOptions.mainFiles = options.mainFiles\r\n            }\r\n            if (options.modules) {\r\n              if (\r\n                !Array.isArray(options.modules) ||\r\n                options.modules.length > 0\r\n              ) {\r\n                throw new Error('modules resolve option is not supported')\r\n              }\r\n              rustOptions.noModules = true\r\n            }\r\n            if (options.restrictions) {\r\n              // TODO This is ignored for now\r\n            }\r\n            if (options.dependencyType) {\r\n              // TODO This is ignored for now\r\n            }\r\n            if (options.preferRelative) {\r\n              if (typeof options.preferRelative !== 'boolean') {\r\n                throw new Error(\r\n                  'preferRelative resolve option must be a boolean'\r\n                )\r\n              }\r\n              rustOptions.preferRelative = options.preferRelative\r\n            }\r\n            return (\r\n              lookupPath: string,\r\n              request: string,\r\n              callback?: (err?: Error, result?: string) => void\r\n            ) => {\r\n              const promise = ipc\r\n                .sendRequest({\r\n                  type: 'resolve',\r\n                  options: rustOptions,\r\n                  lookupPath: toPath(lookupPath),\r\n                  request,\r\n                })\r\n                .then((unknownResult) => {\r\n                  let result = unknownResult as { path: string }\r\n                  if (result && typeof result.path === 'string') {\r\n                    return fromPath(result.path)\r\n                  } else {\r\n                    throw Error(\r\n                      'Expected { path: string } from resolve request'\r\n                    )\r\n                  }\r\n                })\r\n              if (callback) {\r\n                promise\r\n                  .then(\r\n                    (result) => callback(undefined, result),\r\n                    (err) => callback(err)\r\n                  )\r\n                  .catch((err) => {\r\n                    ipc.sendError(err)\r\n                  })\r\n              } else {\r\n                return promise\r\n              }\r\n            }\r\n          },\r\n          emitWarning: makeErrorEmitter('warning', ipc),\r\n          emitError: makeErrorEmitter('error', ipc),\r\n          getLogger(name: unknown) {\r\n            const logFn = (logType: string, ...args: unknown[]) => {\r\n              let trace: StackFrame[] | undefined\r\n              switch (logType) {\r\n                case LogType.warn:\r\n                case LogType.error:\r\n                case LogType.trace:\r\n                case LogType.debug:\r\n                  trace = parseStackTrace(\r\n                    cutOffLoaderExecution(new Error('Trace').stack!)\r\n                      .split('\\n')\r\n                      .slice(3)\r\n                      .join('\\n')\r\n                  )\r\n                  break\r\n                default:\r\n                  // TODO: do we need to handle this?\r\n                  break\r\n              }\r\n              // Batch logs messages to be sent at the end\r\n              logs.push({\r\n                time: Date.now(),\r\n                logType,\r\n                args,\r\n                trace,\r\n              })\r\n            }\r\n            let timers: Map<string, [number, number]> | undefined\r\n            let timersAggregates: Map<string, [number, number]> | undefined\r\n\r\n            // See https://github.com/webpack/webpack/blob/a48c34b34d2d6c44f9b2b221d7baf278d34ac0be/lib/logging/Logger.js#L8\r\n            return {\r\n              error: logFn.bind(this, LogType.error),\r\n              warn: logFn.bind(this, LogType.warn),\r\n              info: logFn.bind(this, LogType.info),\r\n              log: logFn.bind(this, LogType.log),\r\n              debug: logFn.bind(this, LogType.debug),\r\n              assert: (assertion: boolean, ...args: any[]) => {\r\n                if (!assertion) {\r\n                  logFn(LogType.error, ...args)\r\n                }\r\n              },\r\n              trace: logFn.bind(this, LogType.trace),\r\n              clear: logFn.bind(this, LogType.clear),\r\n              status: logFn.bind(this, LogType.status),\r\n              group: logFn.bind(this, LogType.group),\r\n              groupCollapsed: logFn.bind(this, LogType.groupCollapsed),\r\n              groupEnd: logFn.bind(this, LogType.groupEnd),\r\n              profile: logFn.bind(this, LogType.profile),\r\n              profileEnd: logFn.bind(this, LogType.profileEnd),\r\n              time: (label: string) => {\r\n                timers = timers || new Map()\r\n                timers.set(label, process.hrtime())\r\n              },\r\n              timeLog: (label: string) => {\r\n                const prev = timers && timers.get(label)\r\n                if (!prev) {\r\n                  throw new Error(\r\n                    `No such label '${label}' for WebpackLogger.timeLog()`\r\n                  )\r\n                }\r\n                const time = process.hrtime(prev)\r\n                logFn(LogType.time, [label, ...time])\r\n              },\r\n              timeEnd: (label: string) => {\r\n                const prev = timers && timers.get(label)\r\n                if (!prev) {\r\n                  throw new Error(\r\n                    `No such label '${label}' for WebpackLogger.timeEnd()`\r\n                  )\r\n                }\r\n                const time = process.hrtime(prev)\r\n                /** @type {Map<string | undefined, [number, number]>} */\r\n                timers!.delete(label)\r\n                logFn(LogType.time, [label, ...time])\r\n              },\r\n              timeAggregate: (label: string) => {\r\n                const prev = timers && timers.get(label)\r\n                if (!prev) {\r\n                  throw new Error(\r\n                    `No such label '${label}' for WebpackLogger.timeAggregate()`\r\n                  )\r\n                }\r\n                const time = process.hrtime(prev)\r\n                /** @type {Map<string | undefined, [number, number]>} */\r\n                timers!.delete(label)\r\n                /** @type {Map<string | undefined, [number, number]>} */\r\n                timersAggregates = timersAggregates || new Map()\r\n                const current = timersAggregates.get(label)\r\n                if (current !== undefined) {\r\n                  if (time[1] + current[1] > 1e9) {\r\n                    time[0] += current[0] + 1\r\n                    time[1] = time[1] - 1e9 + current[1]\r\n                  } else {\r\n                    time[0] += current[0]\r\n                    time[1] += current[1]\r\n                  }\r\n                }\r\n                timersAggregates.set(label, time)\r\n              },\r\n              timeAggregateEnd: (label: string) => {\r\n                if (timersAggregates === undefined) return\r\n                const time = timersAggregates.get(label)\r\n                if (time === undefined) return\r\n                timersAggregates.delete(label)\r\n                logFn(LogType.time, [label, ...time])\r\n              },\r\n            }\r\n          },\r\n        },\r\n\r\n        loaders: loadersWithOptions.map((loader) => ({\r\n          loader: __turbopack_external_require__.resolve(loader.loader, {\r\n            paths: [contextDir, resourceDir],\r\n          }),\r\n          options: loader.options,\r\n        })),\r\n        readResource: (_filename, callback) => {\r\n          // TODO assuming that filename === resource, but loaders might change that\r\n          let data =\r\n            typeof content === 'string'\r\n              ? Buffer.from(content, 'utf-8')\r\n              : Buffer.from(content.binary, 'base64')\r\n          callback(null, data)\r\n        },\r\n      },\r\n      (err, result) => {\r\n        if (logs.length) {\r\n          ipc.sendInfo({ type: 'log', logs: logs })\r\n          logs.length = 0\r\n        }\r\n        ipc.sendInfo({\r\n          type: 'dependencies',\r\n          envVariables: getReadEnvVariables(),\r\n          filePaths: result.fileDependencies.map(toPath),\r\n          directories: result.contextDependencies.map((dep) => [\r\n            toPath(dep),\r\n            '**',\r\n          ]),\r\n        })\r\n        if (err) return reject(err)\r\n        if (!result.result) return reject(new Error('No result from loaders'))\r\n        const [source, map] = result.result\r\n        resolve({\r\n          source: Buffer.isBuffer(source)\r\n            ? { binary: source.toString('base64') }\r\n            : source,\r\n          map:\r\n            typeof map === 'string'\r\n              ? map\r\n              : typeof map === 'object'\r\n                ? JSON.stringify(map)\r\n                : undefined,\r\n        })\r\n      }\r\n    )\r\n  })\r\n}\r\n\r\nexport { transform as default }\r\n\r\nfunction makeErrorEmitter(\r\n  severity: 'warning' | 'error',\r\n  ipc: Ipc<IpcInfoMessage, IpcRequestMessage>\r\n) {\r\n  return function (error: Error | string) {\r\n    ipc.sendInfo({\r\n      type: 'emittedError',\r\n      severity: severity,\r\n      error: structuredError(error),\r\n    })\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAKA;AACA;AAIA;AACA;;;;;AA4CA,MAAM,EACJ,UAAU,EACX;AAED,MAAM,aAAa,QAAQ,GAAG;AAE9B,MAAM,UAAU,OAAO,MAAM,CAAC;IAC5B,OAAO;IACP,MAAM;IACN,MAAM;IACN,KAAK;IACL,OAAO;IAEP,OAAO;IAEP,OAAO;IACP,gBAAgB;IAChB,UAAU;IAEV,SAAS;IACT,YAAY;IAEZ,MAAM;IAEN,OAAO;IACP,QAAQ;AACV;AAEA,MAAM,aAAa;AAEnB,MAAM,eAAe,CAAC,OAAe;IACnC,MAAM,aAAa,MAAM,KAAK,CAAC;IAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QAC1C,IAAI,UAAU,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO;YAChC,WAAW,MAAM,GAAG;QACtB;IACF;IACA,OAAO,WAAW,IAAI,CAAC;AACzB;AAEA;;;CAGC,GACD,MAAM,wBAAwB,CAAC,QAC7B,aAAa,OAAO;AAEtB,MAAM;IACJ,aAAa;QACX,OAAO,IAAI;IACb;IAEA,QAAW,EAA0B,EAAK;QACxC,OAAO,GAAG,IAAI;IAChB;IAEA,MAAM,aAAgB,EAAuC,EAAc;QACzE,OAAO,MAAM,GAAG,IAAI;IACtB;IAEA,OAAO;QACL;IACF;AACF;AA4BA,MAAM,YAAY,CAChB,KACA,SACA,MACA,OACA,SACA;IAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,WAAW,IAAA,4GAAW,EAAC,YAAY;QACzC,MAAM,cAAc,IAAA,4GAAO,EAAC;QAE5B,MAAM,qBAAqB,QAAQ,GAAG,CAAC,CAAC,SACtC,OAAO,WAAW,WAAW;gBAAE;gBAAQ,SAAS,CAAC;YAAE,IAAI;QAGzD,MAAM,OAKD,EAAE;QAEP,WACE;YACE,UAAU,WAAW;YACrB,SAAS;gBACP,SAAS;oBACP,2EAA2E;oBAC3E,mDAAmD;oBACnD,YAAY;gBACd;gBACA,kBAAkB,IAAI;gBACtB,aAAa;gBACb;gBACA;oBACE,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC;oBAC5C,OAAO,MAAM,OAAO,IAAI,OAAO,MAAM,OAAO,KAAK,WAC7C,MAAM,OAAO,GACb,CAAC;gBACP;gBACA,YAAY,CAAC;oBACX,MAAM,cAAc;wBAClB,aAAa;wBACb,gBAAgB;wBAChB,eAAe;wBACf,YAAY;wBACZ,YAAY;wBACZ,gBAAgB;wBAChB,WAAW;wBACX,WAAW;wBACX,gBAAgB;oBAClB;oBACA,IAAI,QAAQ,KAAK,EAAE;wBACjB,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,KAAK,KAAK,QAAQ,KAAK,CAAC,MAAM,GAAG,GAAG;4BAC7D,MAAM,IAAI,MAAM;wBAClB;oBACF;oBACA,IAAI,QAAQ,WAAW,EAAE;wBACvB,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,WAAW,GAAG;4BACvC,MAAM,IAAI,MAAM;wBAClB;wBACA,YAAY,WAAW,GAAG,QAAQ,WAAW;oBAC/C;oBACA,IAAI,QAAQ,cAAc,EAAE;wBAC1B,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,cAAc,GAAG;4BAC1C,MAAM,IAAI,MACR;wBAEJ;wBACA,YAAY,cAAc,GAAG,QAAQ,cAAc;oBACrD;oBACA,IAAI,QAAQ,gBAAgB,EAAE;wBAC5B,IACE,CAAC,MAAM,OAAO,CAAC,QAAQ,gBAAgB,KACvC,QAAQ,gBAAgB,CAAC,MAAM,GAAG,GAClC;4BACA,MAAM,IAAI,MACR;wBAEJ;wBACA,YAAY,aAAa,GAAG;oBAC9B;oBACA,IAAI,QAAQ,UAAU,EAAE;wBACtB,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,UAAU,GAAG;4BACtC,MAAM,IAAI,MAAM;wBAClB;wBACA,YAAY,UAAU,GAAG,QAAQ,UAAU;oBAC7C;oBACA,IAAI,QAAQ,UAAU,EAAE;wBACtB,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,UAAU,GAAG;4BACtC,MAAM,IAAI,MAAM;wBAClB;wBACA,YAAY,UAAU,GAAG,QAAQ,UAAU;oBAC7C;oBACA,IAAI,QAAQ,aAAa,EAAE;wBACzB,IACE,CAAC,MAAM,OAAO,CAAC,QAAQ,aAAa,KACpC,QAAQ,aAAa,CAAC,MAAM,GAAG,GAC/B;4BACA,MAAM,IAAI,MAAM;wBAClB;wBACA,YAAY,cAAc,GAAG;oBAC/B;oBACA,IAAI,QAAQ,SAAS,EAAE;wBACrB,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,SAAS,GAAG;4BACrC,MAAM,IAAI,MAAM;wBAClB;wBACA,YAAY,SAAS,GAAG,QAAQ,SAAS;oBAC3C;oBACA,IAAI,QAAQ,OAAO,EAAE;wBACnB,IACE,CAAC,MAAM,OAAO,CAAC,QAAQ,OAAO,KAC9B,QAAQ,OAAO,CAAC,MAAM,GAAG,GACzB;4BACA,MAAM,IAAI,MAAM;wBAClB;wBACA,YAAY,SAAS,GAAG;oBAC1B;oBACA,IAAI,QAAQ,YAAY,EAAE;oBACxB,+BAA+B;oBACjC;oBACA,IAAI,QAAQ,cAAc,EAAE;oBAC1B,+BAA+B;oBACjC;oBACA,IAAI,QAAQ,cAAc,EAAE;wBAC1B,IAAI,OAAO,QAAQ,cAAc,KAAK,WAAW;4BAC/C,MAAM,IAAI,MACR;wBAEJ;wBACA,YAAY,cAAc,GAAG,QAAQ,cAAc;oBACrD;oBACA,OAAO,CACL,YACA,SACA;wBAEA,MAAM,UAAU,IACb,WAAW,CAAC;4BACX,MAAM;4BACN,SAAS;4BACT,YAAY,IAAA,iJAAM,EAAC;4BACnB;wBACF,GACC,IAAI,CAAC,CAAC;4BACL,IAAI,SAAS;4BACb,IAAI,UAAU,OAAO,OAAO,IAAI,KAAK,UAAU;gCAC7C,OAAO,IAAA,mJAAQ,EAAC,OAAO,IAAI;4BAC7B,OAAO;gCACL,MAAM,MACJ;4BAEJ;wBACF;wBACF,IAAI,UAAU;4BACZ,QACG,IAAI,CACH,CAAC,SAAW,SAAS,WAAW,SAChC,CAAC,MAAQ,SAAS,MAEnB,KAAK,CAAC,CAAC;gCACN,IAAI,SAAS,CAAC;4BAChB;wBACJ,OAAO;4BACL,OAAO;wBACT;oBACF;gBACF;gBACA,aAAa,iBAAiB,WAAW;gBACzC,WAAW,iBAAiB,SAAS;gBACrC,WAAU,IAAa;oBACrB,MAAM,QAAQ,CAAC,SAAiB,GAAG;wBACjC,IAAI;wBACJ,OAAQ;4BACN,KAAK,QAAQ,IAAI;4BACjB,KAAK,QAAQ,KAAK;4BAClB,KAAK,QAAQ,KAAK;4BAClB,KAAK,QAAQ,KAAK;gCAChB,QAAQ,IAAA,iKAAe,EACrB,sBAAsB,IAAI,MAAM,SAAS,KAAK,EAC3C,KAAK,CAAC,MACN,KAAK,CAAC,GACN,IAAI,CAAC;gCAEV;4BACF;gCAEE;wBACJ;wBACA,4CAA4C;wBAC5C,KAAK,IAAI,CAAC;4BACR,MAAM,KAAK,GAAG;4BACd;4BACA;4BACA;wBACF;oBACF;oBACA,IAAI;oBACJ,IAAI;oBAEJ,gHAAgH;oBAChH,OAAO;wBACL,OAAO,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,KAAK;wBACrC,MAAM,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,IAAI;wBACnC,MAAM,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,IAAI;wBACnC,KAAK,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,GAAG;wBACjC,OAAO,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,KAAK;wBACrC,QAAQ,CAAC,WAAoB,GAAG;4BAC9B,IAAI,CAAC,WAAW;gCACd,MAAM,QAAQ,KAAK,KAAK;4BAC1B;wBACF;wBACA,OAAO,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,KAAK;wBACrC,OAAO,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,KAAK;wBACrC,QAAQ,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,MAAM;wBACvC,OAAO,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,KAAK;wBACrC,gBAAgB,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,cAAc;wBACvD,UAAU,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,QAAQ;wBAC3C,SAAS,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,OAAO;wBACzC,YAAY,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,UAAU;wBAC/C,MAAM,CAAC;4BACL,SAAS,UAAU,IAAI;4BACvB,OAAO,GAAG,CAAC,OAAO,QAAQ,MAAM;wBAClC;wBACA,SAAS,CAAC;4BACR,MAAM,OAAO,UAAU,OAAO,GAAG,CAAC;4BAClC,IAAI,CAAC,MAAM;gCACT,MAAM,IAAI,MACR,CAAC,eAAe,EAAE,MAAM,6BAA6B,CAAC;4BAE1D;4BACA,MAAM,OAAO,QAAQ,MAAM,CAAC;4BAC5B,MAAM,QAAQ,IAAI,EAAE;gCAAC;mCAAU;6BAAK;wBACtC;wBACA,SAAS,CAAC;4BACR,MAAM,OAAO,UAAU,OAAO,GAAG,CAAC;4BAClC,IAAI,CAAC,MAAM;gCACT,MAAM,IAAI,MACR,CAAC,eAAe,EAAE,MAAM,6BAA6B,CAAC;4BAE1D;4BACA,MAAM,OAAO,QAAQ,MAAM,CAAC;4BAC5B,sDAAsD,GACtD,OAAQ,MAAM,CAAC;4BACf,MAAM,QAAQ,IAAI,EAAE;gCAAC;mCAAU;6BAAK;wBACtC;wBACA,eAAe,CAAC;4BACd,MAAM,OAAO,UAAU,OAAO,GAAG,CAAC;4BAClC,IAAI,CAAC,MAAM;gCACT,MAAM,IAAI,MACR,CAAC,eAAe,EAAE,MAAM,mCAAmC,CAAC;4BAEhE;4BACA,MAAM,OAAO,QAAQ,MAAM,CAAC;4BAC5B,sDAAsD,GACtD,OAAQ,MAAM,CAAC;4BACf,sDAAsD,GACtD,mBAAmB,oBAAoB,IAAI;4BAC3C,MAAM,UAAU,iBAAiB,GAAG,CAAC;4BACrC,IAAI,YAAY,WAAW;gCACzB,IAAI,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,KAAK;oCAC9B,IAAI,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,GAAG;oCACxB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,MAAM,OAAO,CAAC,EAAE;gCACtC,OAAO;oCACL,IAAI,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE;oCACrB,IAAI,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE;gCACvB;4BACF;4BACA,iBAAiB,GAAG,CAAC,OAAO;wBAC9B;wBACA,kBAAkB,CAAC;4BACjB,IAAI,qBAAqB,WAAW;4BACpC,MAAM,OAAO,iBAAiB,GAAG,CAAC;4BAClC,IAAI,SAAS,WAAW;4BACxB,iBAAiB,MAAM,CAAC;4BACxB,MAAM,QAAQ,IAAI,EAAE;gCAAC;mCAAU;6BAAK;wBACtC;oBACF;gBACF;YACF;YAEA,SAAS,mBAAmB,GAAG,CAAC,CAAC,SAAW,CAAC;oBAC3C,QAAQ,yDAA+B,OAAO,CAAC,OAAO,MAAM,EAAE;wBAC5D,OAAO;4BAAC;4BAAY;yBAAY;oBAClC;oBACA,SAAS,OAAO,OAAO;gBACzB,CAAC;YACD,cAAc,CAAC,WAAW;gBACxB,0EAA0E;gBAC1E,IAAI,OACF,OAAO,YAAY,WACf,OAAO,IAAI,CAAC,SAAS,WACrB,OAAO,IAAI,CAAC,QAAQ,MAAM,EAAE;gBAClC,SAAS,MAAM;YACjB;QACF,GACA,CAAC,KAAK;YACJ,IAAI,KAAK,MAAM,EAAE;gBACf,IAAI,QAAQ,CAAC;oBAAE,MAAM;oBAAO,MAAM;gBAAK;gBACvC,KAAK,MAAM,GAAG;YAChB;YACA,IAAI,QAAQ,CAAC;gBACX,MAAM;gBACN,cAAc,IAAA,8JAAmB;gBACjC,WAAW,OAAO,gBAAgB,CAAC,GAAG,CAAC,iJAAM;gBAC7C,aAAa,OAAO,mBAAmB,CAAC,GAAG,CAAC,CAAC,MAAQ;wBACnD,IAAA,iJAAM,EAAC;wBACP;qBACD;YACH;YACA,IAAI,KAAK,OAAO,OAAO;YACvB,IAAI,CAAC,OAAO,MAAM,EAAE,OAAO,OAAO,IAAI,MAAM;YAC5C,MAAM,CAAC,QAAQ,IAAI,GAAG,OAAO,MAAM;YACnC,QAAQ;gBACN,QAAQ,OAAO,QAAQ,CAAC,UACpB;oBAAE,QAAQ,OAAO,QAAQ,CAAC;gBAAU,IACpC;gBACJ,KACE,OAAO,QAAQ,WACX,MACA,OAAO,QAAQ,WACb,KAAK,SAAS,CAAC,OACf;YACV;QACF;IAEJ;AACF;;AAIA,SAAS,iBACP,QAA6B,EAC7B,GAA2C;IAE3C,OAAO,SAAU,KAAqB;QACpC,IAAI,QAAQ,CAAC;YACX,MAAM;YACN,UAAU;YACV,OAAO,IAAA,8IAAe,EAAC;QACzB;IACF;AACF"}}]}