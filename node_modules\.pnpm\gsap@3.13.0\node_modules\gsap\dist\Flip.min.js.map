{"version": 3, "file": "Flip.min.js", "sources": ["../src/utils/matrix.js", "../src/Flip.js"], "sourcesContent": ["/*!\n * matrix 3.13.0\n * https://gsap.com\n *\n * Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet _doc, _win, _doc<PERSON><PERSON>, _body,\t_div<PERSON><PERSON>r, _svg<PERSON><PERSON>r, _identityMatrix, _gEl,\n\t_transformProp = \"transform\",\n\t_transformOriginProp = _transformProp + \"Origin\",\n\t_hasOffsetBug,\n\t_setDoc = element => {\n\t\tlet doc = element.ownerDocument || element;\n\t\tif (!(_transformProp in element.style) && \"msTransform\" in element.style) { //to improve compatibility with old Microsoft browsers\n\t\t\t_transformProp = \"msTransform\";\n\t\t\t_transformOriginProp = _transformProp + \"Origin\";\n\t\t}\n\t\twhile (doc.parentNode && (doc = doc.parentNode)) {\t}\n\t\t_win = window;\n\t\t_identityMatrix = new Matrix2D();\n\t\tif (doc) {\n\t\t\t_doc = doc;\n\t\t\t_docElement = doc.documentElement;\n\t\t\t_body = doc.body;\n\t\t\t_gEl = _doc.createElementNS(\"http://www.w3.org/2000/svg\", \"g\");\n\t\t\t// prevent any existing CSS from transforming it\n\t\t\t_gEl.style.transform = \"none\";\n\t\t\t// now test for the offset reporting bug. Use feature detection instead of browser sniffing to make things more bulletproof and future-proof. Hopefully Safari will fix their bug soon.\n\t\t\tlet d1 = doc.createElement(\"div\"),\n\t\t\t\td2 = doc.createElement(\"div\"),\n\t\t\t\troot = doc && (doc.body || doc.firstElementChild);\n\t\t\tif (root && root.appendChild) {\n\t\t\t\troot.appendChild(d1);\n\t\t\t\td1.appendChild(d2);\n\t\t\t\td1.setAttribute(\"style\", \"position:static;transform:translate3d(0,0,1px)\");\n\t\t\t\t_hasOffsetBug = (d2.offsetParent !== d1);\n\t\t\t\troot.removeChild(d1);\n\t\t\t}\n\t\t}\n\t\treturn doc;\n\t},\n\t_forceNonZeroScale = e => { // walks up the element's ancestors and finds any that had their scale set to 0 via GSAP, and changes them to 0.0001 to ensure that measurements work. Firefox has a bug that causes it to incorrectly report getBoundingClientRect() when scale is 0.\n\t\tlet a, cache;\n\t\twhile (e && e !== _body) {\n\t\t\tcache = e._gsap;\n\t\t\tcache && cache.uncache && cache.get(e, \"x\"); // force re-parsing of transforms if necessary\n\t\t\tif (cache && !cache.scaleX && !cache.scaleY && cache.renderTransform) {\n\t\t\t\tcache.scaleX = cache.scaleY = 1e-4;\n\t\t\t\tcache.renderTransform(1, cache);\n\t\t\t\ta ? a.push(cache) : (a = [cache]);\n\t\t\t}\n\t\t\te = e.parentNode;\n\t\t}\n\t\treturn a;\n\t},\n\t// possible future addition: pass an element to _forceDisplay() and it'll walk up all its ancestors and make sure anything with display: none is set to display: block, and if there's no parentNode, it'll add it to the body. It returns an Array that you can then feed to _revertDisplay() to have it revert all the changes it made.\n\t// _forceDisplay = e => {\n\t// \tlet a = [],\n\t// \t\tparent;\n\t// \twhile (e && e !== _body) {\n\t// \t\tparent = e.parentNode;\n\t// \t\t(_win.getComputedStyle(e).display === \"none\" || !parent) && a.push(e, e.style.display, parent) && (e.style.display = \"block\");\n\t// \t\tparent || _body.appendChild(e);\n\t// \t\te = parent;\n\t// \t}\n\t// \treturn a;\n\t// },\n\t// _revertDisplay = a => {\n\t// \tfor (let i = 0; i < a.length; i+=3) {\n\t// \t\ta[i+1] ? (a[i].style.display = a[i+1]) : a[i].style.removeProperty(\"display\");\n\t// \t\ta[i+2] || a[i].parentNode.removeChild(a[i]);\n\t// \t}\n\t// },\n\t_svgTemps = [], //we create 3 elements for SVG, and 3 for other DOM elements and cache them for performance reasons. They get nested in _divContainer and _svgContainer so that just one element is added to the DOM on each successive attempt. Again, performance is key.\n\t_divTemps = [],\n\t_getDocScrollTop = () => _win.pageYOffset  || _doc.scrollTop || _docElement.scrollTop || _body.scrollTop || 0,\n\t_getDocScrollLeft = () => _win.pageXOffset || _doc.scrollLeft || _docElement.scrollLeft || _body.scrollLeft || 0,\n\t_svgOwner = element => element.ownerSVGElement || ((element.tagName + \"\").toLowerCase() === \"svg\" ? element : null),\n\t_isFixed = element => {\n\t\tif (_win.getComputedStyle(element).position === \"fixed\") {\n\t\t\treturn true;\n\t\t}\n\t\telement = element.parentNode;\n\t\tif (element && element.nodeType === 1) { // avoid document fragments which will throw an error.\n\t\t\treturn _isFixed(element);\n\t\t}\n\t},\n\t_createSibling = (element, i) => {\n\t\tif (element.parentNode && (_doc || _setDoc(element))) {\n\t\t\tlet svg = _svgOwner(element),\n\t\t\t\tns = svg ? (svg.getAttribute(\"xmlns\") || \"http://www.w3.org/2000/svg\") : \"http://www.w3.org/1999/xhtml\",\n\t\t\t\ttype = svg ? (i ? \"rect\" : \"g\") : \"div\",\n\t\t\t\tx = i !== 2 ? 0 : 100,\n\t\t\t\ty = i === 3 ? 100 : 0,\n\t\t\t\tcss = \"position:absolute;display:block;pointer-events:none;margin:0;padding:0;\",\n\t\t\t\te = _doc.createElementNS ? _doc.createElementNS(ns.replace(/^https/, \"http\"), type) : _doc.createElement(type);\n\t\t\tif (i) {\n\t\t\t\tif (!svg) {\n\t\t\t\t\tif (!_divContainer) {\n\t\t\t\t\t\t_divContainer = _createSibling(element);\n\t\t\t\t\t\t_divContainer.style.cssText = css;\n\t\t\t\t\t}\n\t\t\t\t\te.style.cssText = css + \"width:0.1px;height:0.1px;top:\" + y + \"px;left:\" + x + \"px\";\n\t\t\t\t\t_divContainer.appendChild(e);\n\n\t\t\t\t} else {\n\t\t\t\t\t_svgContainer || (_svgContainer = _createSibling(element));\n\t\t\t\t\te.setAttribute(\"width\", 0.01);\n\t\t\t\t\te.setAttribute(\"height\", 0.01);\n\t\t\t\t\te.setAttribute(\"transform\", \"translate(\" + x + \",\" + y + \")\");\n\t\t\t\t\t_svgContainer.appendChild(e);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn e;\n\t\t}\n\t\tthrow \"Need document and parent.\";\n\t},\n\t_consolidate = m => { // replaces SVGTransformList.consolidate() because a bug in Firefox causes it to break pointer events. See https://gsap.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\n\t\tlet c = new Matrix2D(),\n\t\t\ti = 0;\n\t\tfor (; i < m.numberOfItems; i++) {\n\t\t\tc.multiply(m.getItem(i).matrix);\n\t\t}\n\t\treturn c;\n\t},\n\t_getCTM = svg => {\n\t\tlet m = svg.getCTM(),\n\t\t\ttransform;\n\t\tif (!m) { // Firefox returns null for getCTM() on root <svg> elements, so this is a workaround using a <g> that we temporarily append.\n\t\t\ttransform = svg.style[_transformProp];\n\t\t\tsvg.style[_transformProp] = \"none\"; // a bug in Firefox causes css transforms to contaminate the getCTM()\n\t\t\tsvg.appendChild(_gEl);\n\t\t\tm = _gEl.getCTM();\n\t\t\tsvg.removeChild(_gEl);\n\t\t\ttransform ? (svg.style[_transformProp] = transform) : svg.style.removeProperty(_transformProp.replace(/([A-Z])/g, \"-$1\").toLowerCase());\n\t\t}\n\t\treturn m || _identityMatrix.clone(); // Firefox will still return null if the <svg> has a width/height of 0 in the browser.\n\t},\n\t_placeSiblings = (element, adjustGOffset) => {\n\t\tlet svg = _svgOwner(element),\n\t\t\tisRootSVG = element === svg,\n\t\t\tsiblings = svg ? _svgTemps : _divTemps,\n\t\t\tparent = element.parentNode,\n\t\t\tappendToEl = parent && !svg && parent.shadowRoot && parent.shadowRoot.appendChild ? parent.shadowRoot : parent,\n\t\t\tcontainer, m, b, x, y, cs;\n\t\tif (element === _win) {\n\t\t\treturn element;\n\t\t}\n\t\tsiblings.length || siblings.push(_createSibling(element, 1), _createSibling(element, 2), _createSibling(element, 3));\n\t\tcontainer = svg ? _svgContainer : _divContainer;\n\t\tif (svg) {\n\t\t\tif (isRootSVG) {\n\t\t\t\tb = _getCTM(element);\n\t\t\t\tx = -b.e / b.a;\n\t\t\t\ty = -b.f / b.d;\n\t\t\t\tm = _identityMatrix;\n\t\t\t} else if (element.getBBox) {\n\t\t\t\tb = element.getBBox();\n\t\t\t\tm = element.transform ? element.transform.baseVal : {}; // IE11 doesn't follow the spec.\n\t\t\t\tm = !m.numberOfItems ? _identityMatrix : m.numberOfItems > 1 ? _consolidate(m) : m.getItem(0).matrix; // don't call m.consolidate().matrix because a bug in Firefox makes pointer events not work when consolidate() is called on the same tick as getBoundingClientRect()! See https://gsap.com/forums/topic/23248-touch-is-not-working-on-draggable-in-firefox-windows-v324/?tab=comments#comment-109800\n\t\t\t\tx = m.a * b.x + m.c * b.y;\n\t\t\t\ty = m.b * b.x + m.d * b.y;\n\t\t\t} else { // may be a <mask> which has no getBBox() so just use defaults instead of throwing errors.\n\t\t\t\tm = new Matrix2D();\n\t\t\t\tx = y = 0;\n\t\t\t}\n\t\t\tif (adjustGOffset && element.tagName.toLowerCase() === \"g\") {\n\t\t\t\tx = y = 0;\n\t\t\t}\n\t\t\t(isRootSVG ? svg : parent).appendChild(container);\n\t\t\tcontainer.setAttribute(\"transform\", \"matrix(\" + m.a + \",\" + m.b + \",\" + m.c + \",\" + m.d + \",\" + (m.e + x) + \",\" + (m.f + y) + \")\");\n\t\t} else {\n\t\t\tx = y = 0;\n\t\t\tif (_hasOffsetBug) { // some browsers (like Safari) have a bug that causes them to misreport offset values. When an ancestor element has a transform applied, it's supposed to treat it as if it's position: relative (new context). Safari botches this, so we need to find the closest ancestor (between the element and its offsetParent) that has a transform applied and if one is found, grab its offsetTop/Left and subtract them to compensate.\n\t\t\t\tm = element.offsetParent;\n\t\t\t\tb = element;\n\t\t\t\twhile (b && (b = b.parentNode) && b !== m && b.parentNode) {\n\t\t\t\t\tif ((_win.getComputedStyle(b)[_transformProp] + \"\").length > 4) {\n\t\t\t\t\t\tx = b.offsetLeft;\n\t\t\t\t\t\ty = b.offsetTop;\n\t\t\t\t\t\tb = 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tcs = _win.getComputedStyle(element);\n\t\t\tif (cs.position !== \"absolute\" && cs.position !== \"fixed\") {\n\t\t\t\tm = element.offsetParent;\n\t\t\t\twhile (parent && parent !== m) { // if there's an ancestor element between the element and its offsetParent that's scrolled, we must factor that in.\n\t\t\t\t\tx += parent.scrollLeft || 0;\n\t\t\t\t\ty += parent.scrollTop || 0;\n\t\t\t\t\tparent = parent.parentNode;\n\t\t\t\t}\n\t\t\t}\n\t\t\tb = container.style;\n\t\t\tb.top = (element.offsetTop - y) + \"px\";\n\t\t\tb.left = (element.offsetLeft - x) + \"px\";\n\t\t\tb[_transformProp] = cs[_transformProp];\n\t\t\tb[_transformOriginProp] = cs[_transformOriginProp];\n\t\t\t// b.border = m.border;\n\t\t\t// b.borderLeftStyle = m.borderLeftStyle;\n\t\t\t// b.borderTopStyle = m.borderTopStyle;\n\t\t\t// b.borderLeftWidth = m.borderLeftWidth;\n\t\t\t// b.borderTopWidth = m.borderTopWidth;\n\t\t\tb.position = cs.position === \"fixed\" ? \"fixed\" : \"absolute\";\n\t\t\tappendToEl.appendChild(container);\n\t\t}\n\t\treturn container;\n\t},\n\t_setMatrix = (m, a, b, c, d, e, f) => {\n\t\tm.a = a;\n\t\tm.b = b;\n\t\tm.c = c;\n\t\tm.d = d;\n\t\tm.e = e;\n\t\tm.f = f;\n\t\treturn m;\n\t};\n\nexport class Matrix2D {\n\tconstructor(a=1, b=0, c=0, d=1, e=0, f=0) {\n\t\t_setMatrix(this, a, b, c, d, e, f);\n\t}\n\n\tinverse() {\n\t\tlet {a, b, c, d, e, f} = this,\n\t\t\tdeterminant = (a * d - b * c) || 1e-10;\n\t\treturn _setMatrix(\n\t\t\tthis,\n\t\t\td / determinant,\n\t\t\t-b / determinant,\n\t\t\t-c / determinant,\n\t\t\ta / determinant,\n\t\t\t(c * f - d * e) / determinant,\n\t\t\t-(a * f - b * e) / determinant\n\t\t);\n\t}\n\n\tmultiply(matrix) {\n\t\tlet {a, b, c, d, e, f} = this,\n\t\t\ta2 = matrix.a,\n\t\t\tb2 = matrix.c,\n\t\t\tc2 = matrix.b,\n\t\t\td2 = matrix.d,\n\t\t\te2 = matrix.e,\n\t\t\tf2 = matrix.f;\n\t\treturn _setMatrix(this,\n\t\t\ta2 * a + c2 * c,\n\t\t\ta2 * b + c2 * d,\n\t\t\tb2 * a + d2 * c,\n\t\t\tb2 * b + d2 * d,\n\t\t\te + e2 * a + f2 * c,\n\t\t\tf + e2 * b + f2 * d);\n\t}\n\n\tclone() {\n\t\treturn new Matrix2D(this.a, this.b, this.c, this.d, this.e, this.f);\n\t}\n\n\tequals(matrix) {\n\t\tlet {a, b, c, d, e, f} = this;\n\t\treturn (a === matrix.a && b === matrix.b && c === matrix.c && d === matrix.d && e === matrix.e && f === matrix.f);\n\t}\n\n\tapply(point, decoratee={}) {\n\t\tlet {x, y} = point,\n\t\t\t{a, b, c, d, e, f} = this;\n\t\tdecoratee.x = (x * a + y * c + e) || 0;\n\t\tdecoratee.y = (x * b + y * d + f) || 0;\n\t\treturn decoratee;\n\t}\n\n}\n\n// Feed in an element and it'll return a 2D matrix (optionally inverted) so that you can translate between coordinate spaces.\n// Inverting lets you translate a global point into a local coordinate space. No inverting lets you go the other way.\n// We needed this to work around various browser bugs, like Firefox doesn't accurately report getScreenCTM() when there\n// are transforms applied to ancestor elements.\n// The matrix math to convert any x/y coordinate is as follows, which is wrapped in a convenient apply() method of Matrix2D above:\n//     tx = m.a * x + m.c * y + m.e\n//     ty = m.b * x + m.d * y + m.f\nexport function getGlobalMatrix(element, inverse, adjustGOffset, includeScrollInFixed) { // adjustGOffset is typically used only when grabbing an element's PARENT's global matrix, and it ignores the x/y offset of any SVG <g> elements because they behave in a special way.\n\tif (!element || !element.parentNode || (_doc || _setDoc(element)).documentElement === element) {\n\t\treturn new Matrix2D();\n\t}\n\tlet zeroScales = _forceNonZeroScale(element),\n\t\tsvg = _svgOwner(element),\n\t\ttemps = svg ? _svgTemps : _divTemps,\n\t\tcontainer = _placeSiblings(element, adjustGOffset),\n\t\tb1 = temps[0].getBoundingClientRect(),\n\t\tb2 = temps[1].getBoundingClientRect(),\n\t\tb3 = temps[2].getBoundingClientRect(),\n\t\tparent = container.parentNode,\n\t\tisFixed = !includeScrollInFixed && _isFixed(element),\n\t\tm = new Matrix2D(\n\t\t\t(b2.left - b1.left) / 100,\n\t\t\t(b2.top - b1.top) / 100,\n\t\t\t(b3.left - b1.left) / 100,\n\t\t\t(b3.top - b1.top) / 100,\n\t\t\tb1.left + (isFixed ? 0 : _getDocScrollLeft()),\n\t\t\tb1.top + (isFixed ? 0 : _getDocScrollTop())\n\t\t);\n\tparent.removeChild(container);\n\tif (zeroScales) {\n\t\tb1 = zeroScales.length;\n\t\twhile (b1--) {\n\t\t\tb2 = zeroScales[b1];\n\t\t\tb2.scaleX = b2.scaleY = 0;\n\t\t\tb2.renderTransform(1, b2);\n\t\t}\n\t}\n\treturn inverse ? m.inverse() : m;\n}\n\nexport { _getDocScrollTop, _getDocScrollLeft, _setDoc, _isFixed, _getCTM };\n\n// export function getMatrix(element) {\n// \t_doc || _setDoc(element);\n// \tlet m = (_win.getComputedStyle(element)[_transformProp] + \"\").substr(7).match(/[-.]*\\d+[.e\\-+]*\\d*[e\\-\\+]*\\d*/g),\n// \t\tis2D = m && m.length === 6;\n// \treturn !m || m.length < 6 ? new Matrix2D() : new Matrix2D(+m[0], +m[1], +m[is2D ? 2 : 4], +m[is2D ? 3 : 5], +m[is2D ? 4 : 12], +m[is2D ? 5 : 13]);\n// }", "/*!\n * Flip 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nimport { getGlobalMatrix, _getDocScrollTop, _getDocScrollLeft, Matrix2D, _setDoc, _getCTM } from \"./utils/matrix.js\";\n\nlet _id = 1,\n\t_toArray, gsap, _batch, _batchAction, _body, _closestTenth, _getStyleSaver,\n\t_forEachBatch = (batch, name) => batch.actions.forEach(a => a.vars[name] && a.vars[name](a)),\n\t_batchLookup = {},\n\t_RAD2DEG = 180 / Math.PI,\n\t_DEG2RAD = Math.PI / 180,\n\t_emptyObj = {},\n\t_dashedNameLookup = {},\n\t_memoizedRemoveProps = {},\n\t_listToArray = list => typeof(list) === \"string\" ? list.split(\" \").join(\"\").split(\",\") : list, // removes extra spaces contaminating the names, returns an Array.\n\t_callbacks = _listToArray(\"onStart,onUpdate,onComplete,onReverseComplete,onInterrupt\"),\n\t_removeProps = _listToArray(\"transform,transformOrigin,width,height,position,top,left,opacity,zIndex,maxWidth,maxHeight,minWidth,minHeight\"),\n\t_getEl = target => _toArray(target)[0] || console.warn(\"Element not found:\", target),\n\t_round = value => Math.round(value * 10000) / 10000 || 0,\n\t_toggleClass = (targets, className, action) => targets.forEach(el => el.classList[action](className)),\n\t_reserved = {zIndex:1, kill:1, simple:1, spin:1, clearProps:1, targets:1, toggleClass:1, onComplete:1, onUpdate:1, onInterrupt:1, onStart:1, delay:1, repeat:1, repeatDelay:1, yoyo:1, scale:1, fade:1, absolute:1, props:1, onEnter:1, onLeave:1, custom:1, paused:1, nested:1, prune:1, absoluteOnLeave: 1},\n\t_fitReserved = {zIndex:1, simple:1, clearProps:1, scale:1, absolute:1, fitChild:1, getVars:1, props:1},\n\t_camelToDashed = p => p.replace(/([A-Z])/g, \"-$1\").toLowerCase(),\n\t_copy = (obj, exclude) => {\n\t\tlet result = {}, p;\n\t\tfor (p in obj) {\n\t\t\texclude[p] || (result[p] = obj[p]);\n\t\t}\n\t\treturn result;\n\t},\n\t_memoizedProps = {},\n\t_memoizeProps = props => {\n\t\tlet p = _memoizedProps[props] = _listToArray(props);\n\t\t_memoizedRemoveProps[props] = p.concat(_removeProps);\n\t\treturn p;\n\t},\n\t_getInverseGlobalMatrix = el => { // integrates caching for improved performance\n\t\tlet cache = el._gsap || gsap.core.getCache(el);\n\t\tif (cache.gmCache === gsap.ticker.frame) {\n\t\t\treturn cache.gMatrix;\n\t\t}\n\t\tcache.gmCache = gsap.ticker.frame;\n\t\treturn (cache.gMatrix = getGlobalMatrix(el, true, false, true));\n\t},\n\t_getDOMDepth = (el, invert, level = 0) => { // In invert is true, the sibling depth is increments of 1, and parent/nesting depth is increments of 1000. This lets us order elements in an Array to reflect document flow.\n\t\tlet parent = el.parentNode,\n\t\t\tinc = 1000 * (10 ** level) * (invert ? -1 : 1),\n\t\t\tl = invert ? -inc * 900 : 0;\n\t\twhile (el) {\n\t\t\tl += inc;\n\t\t\tel = el.previousSibling;\n\t\t}\n\t\treturn parent ? l + _getDOMDepth(parent, invert, level + 1) : l;\n\t},\n\t_orderByDOMDepth = (comps, invert, isElStates) => {\n\t\tcomps.forEach(comp => comp.d = _getDOMDepth(isElStates ? comp.element : comp.t, invert));\n\t\tcomps.sort((c1, c2) => c1.d - c2.d);\n\t\treturn comps;\n\t},\n\t_recordInlineStyles = (elState, props) => { // records the current inline CSS properties into an Array in alternating name/value pairs that's stored in a \"css\" property on the state object so that we can revert later.\n\t\tlet style = elState.element.style,\n\t\t\ta = elState.css = elState.css || [],\n\t\t\ti = props.length,\n\t\t\tp, v;\n\t\twhile (i--) {\n\t\t\tp = props[i];\n\t\t\tv = style[p] || style.getPropertyValue(p);\n\t\t\ta.push(v ? p : _dashedNameLookup[p] || (_dashedNameLookup[p] = _camelToDashed(p)), v);\n\t\t}\n\t\treturn style;\n\t},\n\t_applyInlineStyles = state => {\n\t\tlet css = state.css,\n\t\t\tstyle = state.element.style,\n\t\t\ti = 0;\n\t\tstate.cache.uncache = 1;\n\t\tfor (; i < css.length; i+=2) {\n\t\t\tcss[i+1] ? (style[css[i]] = css[i+1]) : style.removeProperty(css[i]);\n\t\t}\n\t\tif (!css[css.indexOf(\"transform\")+1] && style.translate) { // CSSPlugin adds scale, translate, and rotate inline CSS as \"none\" in order to keep CSS rules from contaminating transforms.\n\t\t\tstyle.removeProperty(\"translate\");\n\t\t\tstyle.removeProperty(\"scale\");\n\t\t\tstyle.removeProperty(\"rotate\");\n\t\t}\n\t},\n\t_setFinalStates = (comps, onlyTransforms) => {\n\t\tcomps.forEach(c => c.a.cache.uncache = 1);\n\t\tonlyTransforms || comps.finalStates.forEach(_applyInlineStyles);\n\t},\n\t_absoluteProps = \"paddingTop,paddingRight,paddingBottom,paddingLeft,gridArea,transition\".split(\",\"), // properties that we must record just\n\t_makeAbsolute = (elState, fallbackNode, ignoreBatch) => {\n\t\tlet { element, width, height, uncache, getProp } = elState,\n\t\t\tstyle = element.style,\n\t\t\ti = 4,\n\t\t\tresult, displayIsNone, cs;\n\t\t(typeof(fallbackNode) !== \"object\") && (fallbackNode = elState);\n\t\tif (_batch && ignoreBatch !== 1) {\n\t\t\t_batch._abs.push({t: element, b: elState, a: elState, sd: 0});\n\t\t\t_batch._final.push(() => (elState.cache.uncache = 1) && _applyInlineStyles(elState));\n\t\t\treturn element;\n\t\t}\n\t\tdisplayIsNone = getProp(\"display\") === \"none\";\n\n\t\tif (!elState.isVisible || displayIsNone) {\n\t\t\tdisplayIsNone && (_recordInlineStyles(elState, [\"display\"]).display = fallbackNode.display);\n\t\t\telState.matrix = fallbackNode.matrix;\n\t\t\telState.width = width = elState.width || fallbackNode.width;\n\t\t\telState.height = height = elState.height || fallbackNode.height;\n\t\t}\n\n\t\t_recordInlineStyles(elState, _absoluteProps);\n\t\tcs = window.getComputedStyle(element);\n\t\twhile (i--) {\n\t\t\tstyle[_absoluteProps[i]] = cs[_absoluteProps[i]]; // record paddings as px-based because if removed from grid, percentage-based ones could be altered.\n\t\t}\n\t\tstyle.gridArea = \"1 / 1 / 1 / 1\";\n\t\tstyle.transition = \"none\";\n\n\t\tstyle.position = \"absolute\";\n\t\tstyle.width = width + \"px\";\n\t\tstyle.height = height + \"px\";\n\t\tstyle.top || (style.top = \"0px\");\n\t\tstyle.left || (style.left = \"0px\");\n\t\tif (uncache) {\n\t\t\tresult = new ElementState(element);\n\t\t} else { // better performance\n\t\t\tresult = _copy(elState, _emptyObj);\n\t\t\tresult.position = \"absolute\";\n\t\t\tif (elState.simple) {\n\t\t\t\tlet bounds = element.getBoundingClientRect();\n\t\t\t\tresult.matrix = new Matrix2D(1, 0, 0, 1, bounds.left + _getDocScrollLeft(), bounds.top + _getDocScrollTop());\n\t\t\t} else {\n\t\t\t\tresult.matrix = getGlobalMatrix(element, false, false, true);\n\t\t\t}\n\t\t}\n\t\tresult = _fit(result, elState, true);\n\t\telState.x = _closestTenth(result.x, 0.01);\n\t\telState.y = _closestTenth(result.y, 0.01);\n\t\treturn element;\n\t},\n\t_filterComps = (comps, targets) => {\n\t\tif (targets !== true) {\n\t\t\ttargets = _toArray(targets);\n\t\t\tcomps = comps.filter(c => {\n\t\t\t\tif (targets.indexOf((c.sd < 0 ? c.b : c.a).element) !== -1) {\n\t\t\t\t    return true;\n\t\t\t\t} else {\n\t\t\t\t\tc.t._gsap.renderTransform(1); // we must force transforms to render on anything that isn't being made position: absolute, otherwise the absolute position happens and then when animation begins it applies transforms which can create a new stacking context, throwing off positioning!\n\t\t\t\t\tif (c.b.isVisible) {\n\t\t\t\t\t\tc.t.style.width = c.b.width + \"px\"; // otherwise things can collapse when contents are made position: absolute.\n\t\t\t\t\t\tc.t.style.height = c.b.height + \"px\";\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t\treturn comps;\n\t},\n\t_makeCompsAbsolute = comps => _orderByDOMDepth(comps, true).forEach(c => (c.a.isVisible || c.b.isVisible) && _makeAbsolute(c.sd < 0 ? c.b : c.a, c.b, 1)),\n\t_findElStateInState = (state, other) => (other && state.idLookup[_parseElementState(other).id]) || state.elementStates[0],\n\t_parseElementState = (elOrNode, props, simple, other) => elOrNode instanceof ElementState ? elOrNode : elOrNode instanceof FlipState ? _findElStateInState(elOrNode, other) : new ElementState(typeof(elOrNode) === \"string\" ? _getEl(elOrNode) || console.warn(elOrNode + \" not found\") : elOrNode, props, simple),\n\t_recordProps = (elState, props) => {\n\t\tlet getProp = gsap.getProperty(elState.element, null, \"native\"),\n\t\t\tobj = elState.props = {},\n\t\t\ti = props.length;\n\t\twhile (i--) {\n\t\t\tobj[props[i]] = (getProp(props[i]) + \"\").trim();\n\t\t}\n\t\tobj.zIndex && (obj.zIndex = parseFloat(obj.zIndex) || 0);\n\t\treturn elState;\n\t},\n\t_applyProps = (element, props) => {\n\t\tlet style = element.style || element, // could pass in a vars object.\n\t\t\tp;\n\t\tfor (p in props) {\n\t\t\tstyle[p] = props[p];\n\t\t}\n\t},\n\t_getID = el => {\n\t\tlet id = el.getAttribute(\"data-flip-id\");\n\t\tid || el.setAttribute(\"data-flip-id\", (id = \"auto-\" + _id++));\n\t\treturn id;\n\t},\n\t_elementsFromElementStates = elStates => elStates.map(elState => elState.element),\n\t_handleCallback = (callback, elStates, tl) => callback && elStates.length && tl.add(callback(_elementsFromElementStates(elStates), tl, new FlipState(elStates, 0, true)), 0),\n\n\t_fit = (fromState, toState, scale, applyProps, fitChild, vars) => {\n\t\tlet { element, cache, parent, x, y } = fromState,\n\t\t\t{ width, height, scaleX, scaleY, rotation, bounds } = toState,\n\t\t\tstyles = vars && _getStyleSaver && _getStyleSaver(element, \"transform,width,height\"), // requires at least 3.11.5\n\t\t\tdimensionState = fromState,\n\t\t\t{e, f} = toState.matrix,\n\t\t\tdeep = fromState.bounds.width !== bounds.width || fromState.bounds.height !== bounds.height || fromState.scaleX !== scaleX || fromState.scaleY !== scaleY || fromState.rotation !== rotation,\n\t\t\tsimple = !deep && fromState.simple && toState.simple && !fitChild,\n\t\t\tskewX, fromPoint, toPoint, getProp, parentMatrix, matrix, bbox;\n\t\tif (simple || !parent) {\n\t\t\tscaleX = scaleY = 1;\n\t\t\trotation = skewX = 0;\n\t\t} else {\n\t\t\tparentMatrix = _getInverseGlobalMatrix(parent);\n\t\t\tmatrix = parentMatrix.clone().multiply(toState.ctm ? toState.matrix.clone().multiply(toState.ctm) : toState.matrix); // root SVG elements have a ctm that we must factor out (for example, viewBox:\"0 0 94 94\" with a width of 200px would scale the internals by 2.127 but when we're matching the size of the root <svg> element itself, that scaling shouldn't factor in!)\n\t\t\trotation = _round(Math.atan2(matrix.b, matrix.a) * _RAD2DEG);\n\t\t\tskewX = _round(Math.atan2(matrix.c, matrix.d) * _RAD2DEG + rotation) % 360; // in very rare cases, minor rounding might end up with 360 which should be 0.\n\t\t\tscaleX = Math.sqrt(matrix.a ** 2 + matrix.b ** 2);\n\t\t\tscaleY = Math.sqrt(matrix.c ** 2 + matrix.d ** 2) * Math.cos(skewX * _DEG2RAD);\n\t\t\tif (fitChild) {\n\t\t\t\tfitChild = _toArray(fitChild)[0];\n\t\t\t\tgetProp = gsap.getProperty(fitChild);\n\t\t\t\tbbox = fitChild.getBBox && typeof(fitChild.getBBox) === \"function\" && fitChild.getBBox();\n\t\t\t\tdimensionState = {scaleX: getProp(\"scaleX\"), scaleY: getProp(\"scaleY\"), width: bbox ? bbox.width : Math.ceil(parseFloat(getProp(\"width\", \"px\"))), height: bbox ? bbox.height : parseFloat(getProp(\"height\", \"px\")) };\n\t\t\t}\n\t\t\tcache.rotation = rotation + \"deg\";\n\t\t\tcache.skewX = skewX + \"deg\";\n\t\t}\n\t\tif (scale) {\n\t\t\tscaleX *= width === dimensionState.width || !dimensionState.width ? 1 : width / dimensionState.width; // note if widths are both 0, we should make scaleX 1 - some elements have box-sizing that incorporates padding, etc. and we don't want it to collapse in that case.\n\t\t\tscaleY *= height === dimensionState.height || !dimensionState.height ? 1 : height / dimensionState.height;\n\t\t\tcache.scaleX = scaleX;\n\t\t\tcache.scaleY = scaleY;\n\t\t} else {\n\t\t\twidth = _closestTenth(width * scaleX / dimensionState.scaleX, 0);\n\t\t\theight = _closestTenth(height * scaleY / dimensionState.scaleY, 0);\n\t\t\telement.style.width = width + \"px\";\n\t\t\telement.style.height = height + \"px\";\n\t\t}\n\t\t// if (fromState.isFixed) { // commented out because it's now taken care of in getGlobalMatrix() with a flag at the end.\n\t\t// \te -= _getDocScrollLeft();\n\t\t// \tf -= _getDocScrollTop();\n\t\t// }\n\t\tapplyProps && _applyProps(element, toState.props);\n\t\tif (simple || !parent) {\n\t\t\tx += e - fromState.matrix.e;\n\t\t\ty += f - fromState.matrix.f;\n\t\t} else if (deep || parent !== toState.parent) {\n\t\t\tcache.renderTransform(1, cache);\n\t\t\tmatrix = getGlobalMatrix(fitChild || element, false, false, true);\n\t\t\tfromPoint = parentMatrix.apply({x: matrix.e, y: matrix.f});\n\t\t\ttoPoint = parentMatrix.apply({x: e, y: f});\n\t\t\tx += toPoint.x - fromPoint.x;\n\t\t\ty += toPoint.y - fromPoint.y;\n\t\t} else { // use a faster/cheaper algorithm if we're just moving x/y\n\t\t\tparentMatrix.e = parentMatrix.f = 0;\n\t\t\ttoPoint = parentMatrix.apply({x: e - fromState.matrix.e, y: f - fromState.matrix.f});\n\t\t\tx += toPoint.x;\n\t\t\ty += toPoint.y;\n\t\t}\n\t\tx = _closestTenth(x, 0.02);\n\t\ty = _closestTenth(y, 0.02);\n\t\tif (vars && !(vars instanceof ElementState)) { // revert\n\t\t\tstyles && styles.revert();\n\t\t} else { // or apply the transform immediately\n\t\t\tcache.x = x + \"px\";\n\t\t\tcache.y = y + \"px\";\n\t\t\tcache.renderTransform(1, cache);\n\t\t}\n\t\tif (vars) {\n\t\t\tvars.x = x;\n\t\t\tvars.y = y;\n\t\t\tvars.rotation = rotation;\n\t\t\tvars.skewX = skewX;\n\t\t\tif (scale) {\n\t\t\t\tvars.scaleX = scaleX;\n\t\t\t\tvars.scaleY = scaleY;\n\t\t\t} else {\n\t\t\t\tvars.width = width;\n\t\t\t\tvars.height = height;\n\t\t\t}\n\t\t}\n\t\treturn vars || cache;\n\t},\n\n\t_parseState = (targetsOrState, vars) => targetsOrState instanceof FlipState ? targetsOrState : new FlipState(targetsOrState, vars),\n\t_getChangingElState = (toState, fromState, id) => {\n\t\tlet to1 = toState.idLookup[id],\n\t\t\tto2 = toState.alt[id];\n\t\treturn to2.isVisible && (!(fromState.getElementState(to2.element) || to2).isVisible || !to1.isVisible) ? to2 : to1;\n\t},\n\t_bodyMetrics = [], _bodyProps = \"width,height,overflowX,overflowY\".split(\",\"), _bodyLocked,\n\t_lockBodyScroll = lock => { // if there's no scrollbar, we should lock that so that measurements don't get affected by temporary repositioning, like if something is centered in the window.\n\t\tif (lock !== _bodyLocked) {\n\t\t\tlet s = _body.style,\n\t\t\t\tw = _body.clientWidth === window.outerWidth,\n\t\t\t\th = _body.clientHeight === window.outerHeight,\n\t\t\t\ti = 4;\n\t\t\tif (lock && (w || h)) {\n\t\t\t\twhile (i--) {\n\t\t\t\t\t_bodyMetrics[i] = s[_bodyProps[i]];\n\t\t\t\t}\n\t\t\t\tif (w) {\n\t\t\t\t\ts.width = _body.clientWidth + \"px\";\n\t\t\t\t\ts.overflowY = \"hidden\";\n\t\t\t\t}\n\t\t\t\tif (h) {\n\t\t\t\t\ts.height = _body.clientHeight + \"px\";\n\t\t\t\t\ts.overflowX = \"hidden\";\n\t\t\t\t}\n\t\t\t\t_bodyLocked= lock;\n\t\t\t} else if (_bodyLocked) {\n\t\t\t\twhile (i--) {\n\t\t\t\t\t_bodyMetrics[i] ? (s[_bodyProps[i]] = _bodyMetrics[i]) : s.removeProperty(_camelToDashed(_bodyProps[i]));\n\t\t\t\t}\n\t\t\t\t_bodyLocked = lock;\n\t\t\t}\n\t\t}\n\t},\n\n\t_fromTo = (fromState, toState, vars, relative) => { // relative is -1 if \"from()\", and 1 if \"to()\"\n\t\t(fromState instanceof FlipState && toState instanceof FlipState) || console.warn(\"Not a valid state object.\");\n\t\tvars = vars || {};\n\t\tlet { clearProps, onEnter, onLeave, absolute, absoluteOnLeave, custom, delay, paused, repeat, repeatDelay, yoyo, toggleClass, nested, zIndex, scale, fade, stagger, spin, prune } = vars,\n\t\t\tprops = (\"props\" in vars ? vars : fromState).props,\n\t\t\ttweenVars = _copy(vars, _reserved),\n\t\t\tanimation = gsap.timeline({ delay, paused, repeat, repeatDelay, yoyo, data: \"isFlip\" }),\n\t\t\tremainingProps = tweenVars,\n\t\t\tentering = [],\n\t\t\tleaving = [],\n\t\t\tcomps = [],\n\t\t\tswapOutTargets = [],\n\t\t\tspinNum = spin === true ? 1 : spin || 0,\n\t\t\tspinFunc = typeof(spin) === \"function\" ? spin : () => spinNum,\n\t\t\tinterrupted = fromState.interrupted || toState.interrupted,\n\t\t\taddFunc = animation[relative !== 1 ? \"to\" : \"from\"],\n\t\t\tv, p, endTime, i, el, comp, state, targets, finalStates, fromNode, toNode, run, a, b;\n\t\t//relative || (toState = (new FlipState(toState.targets, {props: props})).fit(toState, scale));\n\t\tfor (p in toState.idLookup) {\n\t\t\ttoNode = !toState.alt[p] ? toState.idLookup[p] : _getChangingElState(toState, fromState, p);\n\t\t\tel = toNode.element;\n\t\t\tfromNode = fromState.idLookup[p];\n\t\t\tfromState.alt[p] && el === fromNode.element && (fromState.alt[p].isVisible || !toNode.isVisible) && (fromNode = fromState.alt[p]);\n\t\t\tif (fromNode) {\n\t\t\t\tcomp = {t: el, b: fromNode, a: toNode, sd: fromNode.element === el ? 0 : toNode.isVisible ? 1 : -1};\n\t\t\t\tcomps.push(comp);\n\t\t\t\tif (comp.sd) {\n\t\t\t\t\tif (comp.sd < 0) {\n\t\t\t\t\t\tcomp.b = toNode;\n\t\t\t\t\t\tcomp.a = fromNode;\n\t\t\t\t\t}\n\t\t\t\t\t// for swapping elements that got interrupted, we must re-record the inline styles to ensure they're not tainted. Remember, .batch() permits getState() not to force in-progress flips to their end state.\n\t\t\t\t\tinterrupted && _recordInlineStyles(comp.b, props ? _memoizedRemoveProps[props] : _removeProps);\n\t\t\t\t\tfade && comps.push(comp.swap = {t: fromNode.element, b: comp.b, a: comp.a, sd: -comp.sd, swap: comp});\n\t\t\t\t}\n\t\t\t\tel._flip = fromNode.element._flip = _batch ? _batch.timeline : animation;\n\t\t\t} else if (toNode.isVisible) {\n\t\t\t\tcomps.push({t: el, b: _copy(toNode, {isVisible:1}), a: toNode, sd: 0, entering: 1}); // to include it in the \"entering\" Array and do absolute positioning if necessary\n\t\t\t\tel._flip = _batch ? _batch.timeline : animation;\n\t\t\t}\n\t\t}\n\n\t\tprops && (_memoizedProps[props] || _memoizeProps(props)).forEach(p => tweenVars[p] = i => comps[i].a.props[p]);\n\t\tcomps.finalStates = finalStates = [];\n\n\t\trun = () => {\n\t\t\t_orderByDOMDepth(comps);\n\t\t\t_lockBodyScroll(true); // otherwise, measurements may get thrown off when things get fit.\n\t\t\t// TODO: cache the matrix, especially for parent because it'll probably get reused quite a bit, but lock it to a particular cycle(?).\n\t\t\tfor (i = 0; i < comps.length; i++) {\n\t\t\t\tcomp = comps[i];\n\t\t\t\ta = comp.a;\n\t\t\t\tb = comp.b;\n\t\t\t\tif (prune && !a.isDifferent(b) && !comp.entering) { // only flip if things changed! Don't omit it from comps initially because that'd prevent the element from being positioned absolutely (if necessary)\n\t\t\t\t\tcomps.splice(i--, 1);\n\t\t\t\t} else {\n\t\t\t\t\tel = comp.t;\n\t\t\t\t\tnested && !(comp.sd < 0) && i && (a.matrix = getGlobalMatrix(el, false, false, true)); // moving a parent affects the position of children\n\t\t\t\t\tif (b.isVisible && a.isVisible) {\n\t\t\t\t\t\tif (comp.sd < 0) { // swapping OUT (swap direction of -1 is out)\n\t\t\t\t\t\t\tstate = new ElementState(el, props, fromState.simple);\n\t\t\t\t\t\t\t_fit(state, a, scale, 0, 0, state);\n\t\t\t\t\t\t\tstate.matrix = getGlobalMatrix(el, false, false, true);\n\t\t\t\t\t\t\tstate.css = comp.b.css;\n\t\t\t\t\t\t\tcomp.a = a = state;\n\t\t\t\t\t\t\tfade && (el.style.opacity = interrupted ? b.opacity : a.opacity);\n\t\t\t\t\t\t\tstagger && swapOutTargets.push(el);\n\t\t\t\t\t\t} else if (comp.sd > 0 && fade) { // swapping IN (swap direction of 1 is in)\n\t\t\t\t\t\t\tel.style.opacity = interrupted ? a.opacity - b.opacity : \"0\";\n\t\t\t\t\t\t}\n\t\t\t\t\t\t_fit(a, b, scale, props);\n\n\t\t\t\t\t} else if (b.isVisible !== a.isVisible) { // either entering or leaving (one side is invisible)\n\t\t\t\t\t\tif (!b.isVisible) { // entering\n\t\t\t\t\t\t\ta.isVisible && entering.push(a);\n\t\t\t\t\t\t\tcomps.splice(i--, 1);\n\t\t\t\t\t\t} else if (!a.isVisible) { // leaving\n\t\t\t\t\t\t\tb.css = a.css;\n\t\t\t\t\t\t\tleaving.push(b);\n\t\t\t\t\t\t\tcomps.splice(i--, 1);\n\t\t\t\t\t\t\tabsolute && nested && _fit(a, b, scale, props);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (!scale) {\n\t\t\t\t\t\tel.style.maxWidth = Math.max(a.width, b.width) + \"px\";\n\t\t\t\t\t\tel.style.maxHeight = Math.max(a.height, b.height) + \"px\";\n\t\t\t\t\t\tel.style.minWidth = Math.min(a.width, b.width) + \"px\";\n\t\t\t\t\t\tel.style.minHeight = Math.min(a.height, b.height) + \"px\";\n\t\t\t\t\t}\n\t\t\t\t\tnested && toggleClass && el.classList.add(toggleClass);\n\t\t\t\t}\n\t\t\t\tfinalStates.push(a);\n\t\t\t}\n\t\t\tlet classTargets;\n\t\t\tif (toggleClass) {\n\t\t\t\tclassTargets = finalStates.map(s => s.element);\n\t\t\t\tnested && classTargets.forEach(e => e.classList.remove(toggleClass)); // there could be a delay, so don't leave the classes applied (we'll do it in a timeline callback)\n\t\t\t}\n\n\t\t\t_lockBodyScroll(false);\n\n\t\t\tif (scale) {\n\t\t\t\ttweenVars.scaleX = i => comps[i].a.scaleX;\n\t\t\t\ttweenVars.scaleY = i => comps[i].a.scaleY;\n\t\t\t} else {\n\t\t\t\ttweenVars.width = i => comps[i].a.width + \"px\";\n\t\t\t\ttweenVars.height = i => comps[i].a.height + \"px\";\n\t\t\t\ttweenVars.autoRound = vars.autoRound || false;\n\t\t\t}\n\t\t\ttweenVars.x = i => comps[i].a.x + \"px\";\n\t\t\ttweenVars.y = i => comps[i].a.y + \"px\";\n\t\t\ttweenVars.rotation = i => comps[i].a.rotation + (spin ? spinFunc(i, targets[i], targets) * 360 : 0);\n\t\t\ttweenVars.skewX = i => comps[i].a.skewX;\n\n\t\t\ttargets = comps.map(c => c.t);\n\n\t\t\tif (zIndex || zIndex === 0) {\n\t\t\t\ttweenVars.modifiers = {zIndex: () => zIndex};\n\t\t\t\ttweenVars.zIndex = zIndex;\n\t\t\t\ttweenVars.immediateRender = vars.immediateRender !== false;\n\t\t\t}\n\n\t\t\tfade && (tweenVars.opacity = i => comps[i].sd < 0 ? 0 : comps[i].sd > 0 ? comps[i].a.opacity : \"+=0\");\n\n\t\t\tif (swapOutTargets.length) {\n\t\t\t\tstagger = gsap.utils.distribute(stagger);\n\t\t\t\tlet dummyArray = targets.slice(swapOutTargets.length);\n\t\t\t\ttweenVars.stagger = (i, el) => stagger(~swapOutTargets.indexOf(el) ? targets.indexOf(comps[i].swap.t) : i, el, dummyArray);\n\t\t\t}\n\n\t\t\t// // for testing...\n\t\t\t// gsap.delayedCall(vars.data ? 50 : 1, function() {\n\t\t\t// \tanimation.eventCallback(\"onComplete\", () => _setFinalStates(comps, !clearProps));\n\t\t\t// \taddFunc.call(animation, targets, tweenVars, 0).play();\n\t\t\t// });\n\t\t\t// return;\n\n\t\t\t_callbacks.forEach(name => vars[name] && animation.eventCallback(name, vars[name], vars[name + \"Params\"])); // apply callbacks to the timeline, not tweens (because \"custom\" timing can make multiple tweens)\n\n\t\t\tif (custom && targets.length) { // bust out the custom properties as their own tweens so they can use different eases, durations, etc.\n\t\t\t\tremainingProps = _copy(tweenVars, _reserved);\n\t\t\t\tif (\"scale\" in custom) {\n\t\t\t\t\tcustom.scaleX = custom.scaleY = custom.scale;\n\t\t\t\t\tdelete custom.scale;\n\t\t\t\t}\n\t\t\t\tfor (p in custom) {\n\t\t\t\t\tv = _copy(custom[p], _fitReserved);\n\t\t\t\t\tv[p] = tweenVars[p];\n\t\t\t\t\t!(\"duration\" in v) && (\"duration\" in tweenVars) && (v.duration = tweenVars.duration);\n\t\t\t\t\tv.stagger = tweenVars.stagger;\n\t\t\t\t\taddFunc.call(animation, targets, v, 0);\n\t\t\t\t\tdelete remainingProps[p];\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (targets.length || leaving.length || entering.length) {\n\t\t\t\ttoggleClass && animation.add(() => _toggleClass(classTargets, toggleClass, animation._zTime < 0 ? \"remove\" : \"add\"), 0) && !paused && _toggleClass(classTargets, toggleClass, \"add\");\n\t\t\t\ttargets.length && addFunc.call(animation, targets, remainingProps, 0);\n\t\t\t}\n\n\t\t\t_handleCallback(onEnter, entering, animation);\n\t\t\t_handleCallback(onLeave, leaving, animation);\n\n\t\t\tlet batchTl = _batch && _batch.timeline;\n\n\t\t\tif (batchTl) {\n\t\t\t\tbatchTl.add(animation, 0);\n\t\t\t\t_batch._final.push(() => _setFinalStates(comps, !clearProps));\n\t\t\t}\n\n\t\t\tendTime = animation.duration();\n\t\t\tanimation.call(() => {\n\t\t\t\tlet forward = animation.time() >= endTime;\n\t\t\t\tforward && !batchTl && _setFinalStates(comps, !clearProps);\n\t\t\t\ttoggleClass && _toggleClass(classTargets, toggleClass, forward ? \"remove\" : \"add\");\n\t\t\t});\n\t\t};\n\n\t\tabsoluteOnLeave && (absolute = comps.filter(comp => !comp.sd && !comp.a.isVisible && comp.b.isVisible).map(comp => comp.a.element));\n\t\tif (_batch) {\n\t\t\tabsolute && _batch._abs.push(..._filterComps(comps, absolute));\n\t\t\t_batch._run.push(run);\n\t\t} else {\n\t\t\tabsolute && _makeCompsAbsolute(_filterComps(comps, absolute)); // when making absolute, we must go in a very particular order so that document flow changes don't affect things. Don't make it visible if both the before and after states are invisible! There's no point, and it could make things appear visible during the flip that shouldn't be.\n\t\t\trun();\n\t\t}\n\n\t\tlet anim = _batch ? _batch.timeline : animation;\n\t\tanim.revert = () => _killFlip(anim, 1, 1); // a Flip timeline should behave very different when reverting - it should actually jump to the end so that styles get cleared out.\n\n\t\treturn anim;\n\t},\n\t_interrupt = tl => {\n\t\ttl.vars.onInterrupt && tl.vars.onInterrupt.apply(tl, tl.vars.onInterruptParams || []);\n\t\ttl.getChildren(true, false, true).forEach(_interrupt);\n\t},\n\t_killFlip = (tl, action, force) => { // action: 0 = nothing, 1 = complete, 2 = only kill (don't complete)\n\t\tif (tl && tl.progress() < 1 && (!tl.paused() || force)) {\n\t\t\tif (action) {\n\t\t\t\t_interrupt(tl);\n\t\t\t\taction < 2 && tl.progress(1); // we should also kill it in case it was added to a parent timeline.\n\t\t\t\ttl.kill();\n\t\t\t}\n\t\t\treturn true;\n\t\t}\n\t},\n\t_createLookup = state => {\n\t\tlet lookup = state.idLookup = {},\n\t\t\talt = state.alt = {},\n\t\t\telStates = state.elementStates,\n\t\t\ti = elStates.length,\n\t\t\telState;\n\t\twhile (i--) {\n\t\t\telState = elStates[i];\n\t\t\tlookup[elState.id] ? (alt[elState.id] = elState) : (lookup[elState.id] = elState);\n\t\t}\n\t};\n\n\n\n\n\n\nclass FlipState {\n\n\tconstructor(targets, vars, targetsAreElementStates) {\n\t\tthis.props = vars && vars.props;\n\t\tthis.simple = !!(vars && vars.simple);\n\t\tif (targetsAreElementStates) {\n\t\t\tthis.targets = _elementsFromElementStates(targets);\n\t\t\tthis.elementStates = targets;\n\t\t\t_createLookup(this);\n\t\t} else {\n\t\t\tthis.targets = _toArray(targets);\n\t\t\tlet soft = vars && (vars.kill === false || (vars.batch && !vars.kill));\n\t\t\t_batch && !soft && _batch._kill.push(this);\n\t\t\tthis.update(soft || !!_batch); // when batching, don't force in-progress flips to their end; we need to do that AFTER all getStates() are called.\n\t\t}\n\t}\n\n\tupdate(soft) {\n\t\tthis.elementStates = this.targets.map(el => new ElementState(el, this.props, this.simple));\n\t\t_createLookup(this);\n\t\tthis.interrupt(soft);\n\t\tthis.recordInlineStyles();\n\t\treturn this;\n\t}\n\n\tclear() {\n\t\tthis.targets.length = this.elementStates.length = 0;\n\t\t_createLookup(this);\n\t\treturn this;\n\t}\n\n\tfit(state, scale, nested) {\n\t\tlet elStatesInOrder = _orderByDOMDepth(this.elementStates.slice(0), false, true),\n\t\t\ttoElStates = (state || this).idLookup,\n\t\t\ti = 0,\n\t\t\tfromNode, toNode;\n\t\tfor (; i < elStatesInOrder.length; i++) {\n\t\t\tfromNode = elStatesInOrder[i];\n\t\t\tnested && (fromNode.matrix = getGlobalMatrix(fromNode.element, false, false, true)); // moving a parent affects the position of children\n\t\t\ttoNode = toElStates[fromNode.id];\n\t\t\ttoNode && _fit(fromNode, toNode, scale, true, 0, fromNode);\n\t\t\tfromNode.matrix = getGlobalMatrix(fromNode.element, false, false, true);\n\t\t}\n\t\treturn this;\n\t}\n\n\tgetProperty(element, property) {\n\t\tlet es = this.getElementState(element) || _emptyObj;\n\t\treturn (property in es ? es : es.props || _emptyObj)[property];\n\t}\n\n\tadd(state) {\n\t\tlet i = state.targets.length,\n\t\t\tlookup = this.idLookup,\n\t\t\talt = this.alt,\n\t\t\tindex, es, es2;\n\t\twhile (i--) {\n\t\t\tes = state.elementStates[i];\n\t\t\tes2 = lookup[es.id];\n\t\t\tif (es2 && (es.element === es2.element || (alt[es.id] && alt[es.id].element === es.element))) { // if the flip id is already in this FlipState, replace it!\n\t\t\t\tindex = this.elementStates.indexOf(es.element === es2.element ? es2 : alt[es.id]);\n\t\t\t\tthis.targets.splice(index, 1, state.targets[i]);\n\t\t\t\tthis.elementStates.splice(index, 1, es);\n\t\t\t} else {\n\t\t\t\tthis.targets.push(state.targets[i]);\n\t\t\t\tthis.elementStates.push(es);\n\t\t\t}\n\t\t}\n\t\tstate.interrupted && (this.interrupted = true);\n\t\tstate.simple || (this.simple = false);\n\t\t_createLookup(this);\n\t\treturn this;\n\t}\n\n\tcompare(state) {\n\t\tlet l1 = state.idLookup,\n\t\t\tl2 = this.idLookup,\n\t\t\tunchanged = [],\n\t\t\tchanged = [],\n\t\t\tenter = [],\n\t\t\tleave = [],\n\t\t\ttargets = [],\n\t\t\ta1 = state.alt,\n\t\t\ta2 = this.alt,\n\t\t\tplace = (s1, s2, el) => (s1.isVisible !== s2.isVisible ? (s1.isVisible ? enter : leave) : s1.isVisible ? changed : unchanged).push(el) && targets.push(el),\n\t\t\tplaceIfDoesNotExist = (s1, s2, el) => targets.indexOf(el) < 0 && place(s1, s2, el),\n\t\t\ts1, s2, p, el, s1Alt, s2Alt, c1, c2;\n\t\tfor (p in l1) {\n\t\t\ts1Alt = a1[p];\n\t\t\ts2Alt = a2[p];\n\t\t\ts1 = !s1Alt ? l1[p] : _getChangingElState(state, this, p);\n\t\t\tel = s1.element;\n\t\t\ts2 = l2[p];\n\t\t\tif (s2Alt) {\n\t\t\t\tc2 = s2.isVisible || (!s2Alt.isVisible && el === s2.element) ? s2 : s2Alt;\n\t\t\t\tc1 = s1Alt && !s1.isVisible && !s1Alt.isVisible && c2.element === s1Alt.element ? s1Alt : s1;\n\t\t\t\t//c1.element !== c2.element && c1.element === s2.element && (c2 = s2);\n\t\t\t\tif (c1.isVisible && c2.isVisible && c1.element !== c2.element) { // swapping, so force into \"changed\" array\n\t\t\t\t\t(c1.isDifferent(c2) ? changed : unchanged).push(c1.element, c2.element);\n\t\t\t\t\ttargets.push(c1.element, c2.element);\n\t\t\t\t} else {\n\t\t\t\t\tplace(c1, c2, c1.element);\n\t\t\t\t}\n\t\t\t\ts1Alt && c1.element === s1Alt.element && (s1Alt = l1[p]);\n\t\t\t\tplaceIfDoesNotExist(c1.element !== s2.element && s1Alt ? s1Alt : c1, s2, s2.element);\n\t\t\t\tplaceIfDoesNotExist(s1Alt && s1Alt.element === s2Alt.element ? s1Alt : c1, s2Alt, s2Alt.element);\n\t\t\t\ts1Alt && placeIfDoesNotExist(s1Alt, s2Alt.element === s1Alt.element ? s2Alt : s2, s1Alt.element);\n\t\t\t} else {\n\t\t\t\t!s2 ? enter.push(el) : !s2.isDifferent(s1) ? unchanged.push(el) : place(s1, s2, el);\n\t\t\t\ts1Alt && placeIfDoesNotExist(s1Alt, s2, s1Alt.element);\n\t\t\t}\n\t\t}\n\t\tfor (p in l2) {\n\t\t\tif (!l1[p]) {\n\t\t\t\tleave.push(l2[p].element);\n\t\t\t\ta2[p] && leave.push(a2[p].element);\n\t\t\t}\n\t\t}\n\t\treturn {changed, unchanged, enter, leave};\n\t}\n\n\trecordInlineStyles() {\n\t\tlet props = _memoizedRemoveProps[this.props] || _removeProps,\n\t\t\ti = this.elementStates.length;\n\t\twhile (i--) {\n\t\t\t_recordInlineStyles(this.elementStates[i], props);\n\t\t}\n\t}\n\n\tinterrupt(soft) { // soft = DON'T force in-progress flip animations to completion (like when running a batch, we can't immediately kill flips when getting states because it could contaminate positioning and other .getState() calls that will run in the batch (we kill AFTER all the .getState() calls complete).\n\t\tlet timelines = [];\n\t\tthis.targets.forEach(t => {\n\t\t\tlet tl = t._flip,\n\t\t\t\tfoundInProgress = _killFlip(tl, soft ? 0 : 1);\n\t\t\tsoft && foundInProgress && timelines.indexOf(tl) < 0 && tl.add(() => this.updateVisibility());\n\t\t\tfoundInProgress && timelines.push(tl);\n\t\t});\n\t\t!soft && timelines.length && this.updateVisibility(); // if we found an in-progress Flip animation, we must record all the values in their current state at that point BUT we should update the isVisible value AFTER pushing that flip to completion so that elements that are entering or leaving will populate those Arrays properly.\n\t\tthis.interrupted || (this.interrupted = !!timelines.length);\n\t}\n\n\tupdateVisibility() {\n\t\tthis.elementStates.forEach(es => {\n\t\t\tlet b = es.element.getBoundingClientRect();\n\t\t\tes.isVisible = !!(b.width || b.height || b.top || b.left);\n\t\t\tes.uncache = 1;\n\t\t});\n\t}\n\n\tgetElementState(element) {\n\t\treturn this.elementStates[this.targets.indexOf(_getEl(element))];\n\t}\n\n\tmakeAbsolute() {\n\t\treturn _orderByDOMDepth(this.elementStates.slice(0), true, true).map(_makeAbsolute);\n\t}\n\n}\n\n\n\nclass ElementState {\n\n\tconstructor(element, props, simple) {\n\t\tthis.element = element;\n\t\tthis.update(props, simple);\n\t}\n\n\tisDifferent(state) {\n\t\tlet b1 = this.bounds,\n\t\t\tb2 = state.bounds;\n\t\treturn b1.top !== b2.top || b1.left !== b2.left || b1.width !== b2.width || b1.height !== b2.height || !this.matrix.equals(state.matrix) || this.opacity !== state.opacity || (this.props && state.props && JSON.stringify(this.props) !== JSON.stringify(state.props));\n\t}\n\n\tupdate(props, simple) {\n\t\tlet self = this,\n\t\t\telement = self.element,\n\t\t\tgetProp = gsap.getProperty(element),\n\t\t\tcache = gsap.core.getCache(element),\n\t\t\tbounds = element.getBoundingClientRect(),\n\t\t\tbbox = element.getBBox && typeof(element.getBBox) === \"function\" && element.nodeName.toLowerCase() !== \"svg\" && element.getBBox(),\n\t\t\tm = simple ? new Matrix2D(1, 0, 0, 1, bounds.left + _getDocScrollLeft(), bounds.top + _getDocScrollTop()) : getGlobalMatrix(element, false, false, true);\n\t\tcache.uncache = 1; // in case there are CSS rules that affect the element. Example: https://gsap.com/community/forums/topic/44321-bug-on-fixed-position-using-flip/\n\t\tself.getProp = getProp;\n\t\tself.element = element;\n\t\tself.id = _getID(element);\n\t\tself.matrix = m;\n\t\tself.cache = cache;\n\t\tself.bounds = bounds;\n\t\tself.isVisible = !!(bounds.width || bounds.height || bounds.left || bounds.top);\n\t\tself.display = getProp(\"display\");\n\t\tself.position = getProp(\"position\");\n\t\tself.parent = element.parentNode;\n\t\tself.x = getProp(\"x\");\n\t\tself.y = getProp(\"y\");\n\t\tself.scaleX = cache.scaleX;\n\t\tself.scaleY = cache.scaleY;\n\t\tself.rotation = getProp(\"rotation\");\n\t\tself.skewX = getProp(\"skewX\");\n\t\tself.opacity = getProp(\"opacity\");\n\t\tself.width =  bbox ? bbox.width : _closestTenth(getProp(\"width\", \"px\"), 0.04); // round up to the closest 0.1 so that text doesn't wrap.\n\t\tself.height = bbox ? bbox.height : _closestTenth(getProp(\"height\", \"px\"), 0.04);\n\t\tprops && _recordProps(self, _memoizedProps[props] || _memoizeProps(props));\n\t\tself.ctm = element.getCTM && element.nodeName.toLowerCase() === \"svg\" && _getCTM(element).inverse();\n\t\tself.simple = simple || (_round(m.a) === 1 && !_round(m.b) && !_round(m.c) && _round(m.d) === 1); // allows us to speed through some other tasks if it's not scale/rotated\n\t\tself.uncache = 0;\n\t}\n\n}\n\nclass FlipAction {\n\tconstructor(vars, batch) {\n\t\tthis.vars = vars;\n\t\tthis.batch = batch;\n\t\tthis.states = [];\n\t\tthis.timeline = batch.timeline;\n\t}\n\n\tgetStateById(id) {\n\t\tlet i = this.states.length;\n\t\twhile (i--) {\n\t\t\tif (this.states[i].idLookup[id]) {\n\t\t\t\treturn this.states[i];\n\t\t\t}\n\t\t}\n\t}\n\n\tkill() {\n\t\tthis.batch.remove(this);\n\t}\n}\n\nclass FlipBatch {\n\tconstructor(id) {\n\t\tthis.id = id;\n\t\tthis.actions = [];\n\t\tthis._kill = [];\n\t\tthis._final = [];\n\t\tthis._abs = [];\n\t\tthis._run = [];\n\t\tthis.data = {};\n\t\tthis.state = new FlipState();\n\t\tthis.timeline = gsap.timeline();\n\t}\n\n\tadd(config) {\n\t\tlet result = this.actions.filter(action => action.vars === config);\n\t\tif (result.length) {\n\t\t\treturn result[0];\n\t\t}\n\t\tresult = new FlipAction(typeof(config) === \"function\" ? {animate: config} : config, this);\n\t\tthis.actions.push(result);\n\t\treturn result;\n\t}\n\n\tremove(action) {\n\t\tlet i = this.actions.indexOf(action);\n\t\ti >= 0 && this.actions.splice(i, 1);\n\t\treturn this;\n\t}\n\n\tgetState(merge) {\n\t\tlet prevBatch = _batch,\n\t\t\tprevAction = _batchAction;\n\t\t_batch = this;\n\t\tthis.state.clear();\n\t\tthis._kill.length = 0;\n\t\tthis.actions.forEach(action => {\n\t\t\tif (action.vars.getState) {\n\t\t\t\taction.states.length = 0;\n\t\t\t\t_batchAction = action;\n\t\t\t\taction.state = action.vars.getState(action);\n\t\t\t}\n\t\t\tmerge && action.states.forEach(s => this.state.add(s));\n\t\t});\n\t\t_batchAction = prevAction;\n\t\t_batch = prevBatch;\n\t\tthis.killConflicts();\n\t\treturn this;\n\t}\n\n\tanimate() {\n\t\tlet prevBatch = _batch,\n\t\t\ttl = this.timeline,\n\t\t\ti = this.actions.length,\n\t\t\tfinalStates, endTime;\n\t\t_batch = this;\n\t\ttl.clear();\n\t\tthis._abs.length = this._final.length = this._run.length = 0;\n\t\tthis.actions.forEach(a => {\n\t\t\ta.vars.animate && a.vars.animate(a);\n\t\t\tlet onEnter = a.vars.onEnter,\n\t\t\t\tonLeave = a.vars.onLeave,\n\t\t\t\ttargets = a.targets, s, result;\n\t\t\tif (targets && targets.length && (onEnter || onLeave)) {\n\t\t\t\ts = new FlipState();\n\t\t\t\ta.states.forEach(state => s.add(state));\n\t\t\t\tresult = s.compare(Flip.getState(targets));\n\t\t\t\tresult.enter.length && onEnter && onEnter(result.enter);\n\t\t\t\tresult.leave.length && onLeave && onLeave(result.leave);\n\t\t\t}\n\t\t});\n\t\t_makeCompsAbsolute(this._abs);\n\t\tthis._run.forEach(f => f());\n\t\tendTime = tl.duration();\n\t\tfinalStates = this._final.slice(0);\n\t\ttl.add(() => {\n\t\t\tif (endTime <= tl.time()) { // only call if moving forward in the timeline (in case it's nested in a timeline that gets reversed)\n\t\t\t\tfinalStates.forEach(f => f());\n\t\t\t\t_forEachBatch(this, \"onComplete\");\n\t\t\t}\n\t\t});\n\t\t_batch = prevBatch;\n\t\twhile (i--) {\n\t\t\tthis.actions[i].vars.once && this.actions[i].kill();\n\t\t}\n\t\t_forEachBatch(this, \"onStart\");\n\t\ttl.restart();\n\t\treturn this;\n\t}\n\n\tloadState(done) {\n\t\tdone || (done = () => 0);\n\t\tlet queue = [];\n\t\tthis.actions.forEach(c => {\n\t\t\tif (c.vars.loadState) {\n\t\t\t\tlet i, f = targets => {\n\t\t\t\t\ttargets && (c.targets = targets);\n\t\t\t\t\ti = queue.indexOf(f);\n\t\t\t\t\tif (~i) {\n\t\t\t\t\t\tqueue.splice(i, 1);\n\t\t\t\t\t\tqueue.length || done();\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\tqueue.push(f);\n\t\t\t\tc.vars.loadState(f);\n\t\t\t}\n\t\t});\n\t\tqueue.length || done();\n\t\treturn this;\n\t}\n\n\tsetState() {\n\t\tthis.actions.forEach(c => c.targets = c.vars.setState && c.vars.setState(c));\n\t\treturn this;\n\t}\n\n\tkillConflicts(soft) {\n\t\tthis.state.interrupt(soft);\n\t\tthis._kill.forEach(state => state.interrupt(soft));\n\t\treturn this;\n\t}\n\n\trun(skipGetState, merge) {\n\t\tif (this !== _batch) {\n\t\t\tskipGetState || this.getState(merge);\n\t\t\tthis.loadState(() => {\n\t\t\t\tif (!this._killed) {\n\t\t\t\t\tthis.setState();\n\t\t\t\t\tthis.animate();\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t\treturn this;\n\t}\n\n\tclear(stateOnly) {\n\t\tthis.state.clear();\n\t\tstateOnly || (this.actions.length = 0);\n\t}\n\n\tgetStateById(id) {\n\t\tlet i = this.actions.length,\n\t\t\ts;\n\t\twhile (i--) {\n\t\t\ts = this.actions[i].getStateById(id);\n\t\t\tif (s) {\n\t\t\t\treturn s;\n\t\t\t}\n\t\t}\n\t\treturn this.state.idLookup[id] && this.state;\n\t}\n\n\tkill() {\n\t\tthis._killed = 1;\n\t\tthis.clear();\n\t\tdelete _batchLookup[this.id];\n\t}\n}\n\n\nexport class Flip {\n\n\tstatic getState(targets, vars) {\n\t\tlet state = _parseState(targets, vars);\n\t\t_batchAction && _batchAction.states.push(state);\n\t\tvars && vars.batch && Flip.batch(vars.batch).state.add(state);\n\t\treturn state;\n\t}\n\n\tstatic from(state, vars) {\n\t\tvars = vars || {};\n\t\t(\"clearProps\" in vars) || (vars.clearProps = true);\n\t\treturn _fromTo(state, _parseState(vars.targets || state.targets, {props: vars.props || state.props, simple: vars.simple, kill: !!vars.kill}), vars, -1);\n\t}\n\n\tstatic to(state, vars) {\n\t\treturn _fromTo(state, _parseState(vars.targets || state.targets, {props: vars.props || state.props, simple: vars.simple, kill: !!vars.kill}), vars, 1);\n\t}\n\n\tstatic fromTo(fromState, toState, vars) {\n\t\treturn _fromTo(fromState, toState, vars);\n\t}\n\n\tstatic fit(fromEl, toEl, vars) {\n\t\tlet v = vars ? _copy(vars, _fitReserved) : {},\n\t\t\t{absolute, scale, getVars, props, runBackwards, onComplete, simple} = vars || v,\n\t\t\tfitChild = vars && vars.fitChild && _getEl(vars.fitChild),\n\t\t\tbefore = _parseElementState(toEl, props, simple, fromEl),\n\t\t\tafter = _parseElementState(fromEl, 0, simple, before),\n\t\t\tinlineProps = props ? _memoizedRemoveProps[props] : _removeProps,\n\t\t\tctx = gsap.context();\n\t\tprops && _applyProps(v, before.props);\n\t\t_recordInlineStyles(after, inlineProps);\n\t\tif (runBackwards) {\n\t\t\t(\"immediateRender\" in v) || (v.immediateRender = true);\n\t\t\tv.onComplete = function() {\n\t\t\t\t_applyInlineStyles(after);\n\t\t\t\tonComplete && onComplete.apply(this, arguments);\n\t\t\t};\n\t\t}\n\t\tabsolute && _makeAbsolute(after, before);\n\t\tv = _fit(after, before, scale || fitChild, !v.duration && props, fitChild, v.duration || getVars ? v : 0);\n\t\ttypeof(vars) === \"object\" && \"zIndex\" in vars && (v.zIndex = vars.zIndex);\n\t\tctx && !getVars && ctx.add(() => () => _applyInlineStyles(after));\n\t\treturn getVars ? v : v.duration ? gsap.to(after.element, v) : null;\n\t}\n\n\tstatic makeAbsolute(targetsOrStates, vars) {\n\t\treturn (targetsOrStates instanceof FlipState ? targetsOrStates : new FlipState(targetsOrStates, vars)).makeAbsolute();\n\t}\n\n\tstatic batch(id) {\n\t\tid || (id = \"default\");\n\t\treturn _batchLookup[id] || (_batchLookup[id] = new FlipBatch(id));\n\t}\n\n\tstatic killFlipsOf(targets, complete) {\n\t\t(targets instanceof FlipState ? targets.targets : _toArray(targets)).forEach(t => t && _killFlip(t._flip, complete !== false ? 1 : 2));\n\t}\n\n\tstatic isFlipping(target) {\n\t\tlet f = Flip.getByTarget(target);\n\t\treturn !!f && f.isActive();\n\t}\n\n\tstatic getByTarget(target) {\n\t\treturn (_getEl(target) || _emptyObj)._flip;\n\t}\n\n\tstatic getElementState(target, props) {\n\t\treturn new ElementState(_getEl(target), props);\n\t}\n\n\tstatic convertCoordinates(fromElement, toElement, point) {\n\t\tlet m = getGlobalMatrix(toElement, true, true).multiply(getGlobalMatrix(fromElement));\n\t\treturn point ? m.apply(point) : m;\n\t}\n\n\n\tstatic register(core) {\n\t\t_body = typeof(document) !== \"undefined\" && document.body;\n\t\tif (_body) {\n\t\t\tgsap = core;\n\t\t\t_setDoc(_body);\n\t\t\t_toArray = gsap.utils.toArray;\n\t\t\t_getStyleSaver = gsap.core.getStyleSaver;\n\t\t\tlet snap = gsap.utils.snap(0.1);\n\t\t\t_closestTenth = (value, add) => snap(parseFloat(value) + add);\n\t\t}\n\t}\n}\n\nFlip.version = \"3.13.0\";\n\n// function whenImagesLoad(el, func) {\n// \tlet pending = [],\n// \t\tonLoad = e => {\n// \t\t\tpending.splice(pending.indexOf(e.target), 1);\n// \t\t\te.target.removeEventListener(\"load\", onLoad);\n// \t\t\tpending.length || func();\n// \t\t};\n// \tgsap.utils.toArray(el.tagName.toLowerCase() === \"img\" ? el : el.querySelectorAll(\"img\")).forEach(img => img.complete || img.addEventListener(\"load\", onLoad) || pending.push(img));\n// \tpending.length || func();\n// }\n\ntypeof(window) !== \"undefined\" && window.gsap && window.gsap.registerPlugin(Flip);\n\nexport { Flip as default };"], "names": ["_setDoc", "element", "doc", "ownerDocument", "_transformProp", "style", "_transformOriginProp", "parentNode", "_win", "window", "_identityMatrix", "Matrix2D", "_doc<PERSON>lement", "_doc", "documentElement", "_body", "body", "_gEl", "createElementNS", "transform", "d1", "createElement", "d2", "root", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "setAttribute", "_hasOffsetBug", "offsetParent", "<PERSON><PERSON><PERSON><PERSON>", "_getDocScrollTop", "pageYOffset", "scrollTop", "_getDocScrollLeft", "pageXOffset", "scrollLeft", "_svgOwner", "ownerSVGElement", "tagName", "toLowerCase", "_createSibling", "i", "svg", "ns", "getAttribute", "type", "x", "y", "css", "e", "replace", "_svgContainer", "_divContainer", "cssText", "_getCTM", "m", "getCTM", "removeProperty", "clone", "_placeSiblings", "adjustGOffset", "container", "b", "cs", "isRootSVG", "siblings", "_svgTemps", "_divTemps", "parent", "appendToEl", "shadowRoot", "length", "push", "a", "f", "d", "getBBox", "baseVal", "numberOfItems", "_consolidate", "c", "multiply", "getItem", "matrix", "getComputedStyle", "offsetLeft", "offsetTop", "position", "top", "left", "_setMatrix", "inverse", "this", "determinant", "a2", "b2", "c2", "e2", "f2", "equals", "apply", "point", "decoratee", "getGlobalMatrix", "includeScrollInFixed", "zeroScales", "_forceNonZeroScale", "cache", "_gsap", "uncache", "get", "scaleX", "scaleY", "renderTransform", "temps", "b1", "getBoundingClientRect", "b3", "isFixed", "_isFixed", "nodeType", "_forEachBatch", "batch", "name", "actions", "for<PERSON>ach", "vars", "_listToArray", "list", "split", "join", "_getEl", "target", "_toArray", "console", "warn", "_round", "value", "Math", "round", "_toggleClass", "targets", "className", "action", "el", "classList", "_camelToDashed", "p", "_copy", "obj", "exclude", "result", "_memoizeProps", "props", "_memoizedProps", "_memoizedRemoveProps", "concat", "_removeProps", "_orderByDOMDepth", "comps", "invert", "isElStates", "comp", "_getD<PERSON><PERSON><PERSON>h", "level", "inc", "l", "previousSibling", "t", "sort", "c1", "_recordInlineStyles", "elState", "v", "getPropertyValue", "_dashedNameLookup", "_applyInlineStyles", "state", "indexOf", "translate", "_setFinalStates", "onlyTransforms", "finalStates", "_makeAbsolute", "fallbackNode", "ignoreBatch", "displayIsNone", "width", "height", "getProp", "_batch", "_abs", "sd", "_final", "isVisible", "display", "_absoluteProps", "gridArea", "transition", "ElementState", "_emptyObj", "simple", "bounds", "_fit", "_closestTenth", "_filterComps", "filter", "_makeCompsAbsolute", "_applyProps", "_elementsFromElementStates", "elStates", "map", "_handleCallback", "callback", "tl", "add", "FlipState", "_parseState", "targetsOrState", "_getChangingElState", "toState", "fromState", "id", "to1", "idLookup", "to2", "alt", "getElementState", "_lockBodyScroll", "lock", "_bodyLocked", "s", "w", "clientWidth", "outerWidth", "h", "clientHeight", "outerHeight", "_bodyMetrics", "_bodyProps", "overflowY", "overflowX", "_fromTo", "relative", "endTime", "fromNode", "toNode", "run", "clearProps", "onEnter", "onLeave", "absolute", "absoluteOnLeave", "custom", "delay", "paused", "repeat", "repeatDelay", "yoyo", "toggleClass", "nested", "zIndex", "scale", "fade", "stagger", "spin", "prune", "tweenVars", "_reserved", "animation", "gsap", "timeline", "data", "remainingProps", "entering", "leaving", "swapOutTargets", "spinNum", "spinFunc", "interrupted", "addFunc", "swap", "_flip", "isDifferent", "opacity", "splice", "max<PERSON><PERSON><PERSON>", "max", "maxHeight", "min<PERSON><PERSON><PERSON>", "min", "minHeight", "classTargets", "remove", "autoRound", "rotation", "skewX", "modifiers", "immediateRender", "utils", "distribute", "dummy<PERSON><PERSON><PERSON>", "slice", "_callbacks", "eventCallback", "_fitReserved", "duration", "call", "_zTime", "batchTl", "forward", "time", "_run", "anim", "revert", "_killFlip", "_createLookup", "lookup", "elementStates", "_batchAction", "_getStyleSaver", "_id", "_batchLookup", "_RAD2DEG", "PI", "_DEG2RAD", "kill", "onComplete", "onUpdate", "onInterrupt", "onStart", "<PERSON><PERSON><PERSON><PERSON>", "getVars", "_parseElementState", "elOrNode", "other", "_findElStateInState", "applyProps", "fromPoint", "toPoint", "parentMatrix", "bbox", "styles", "dimensionState", "deep", "_getInverseGlobalMatrix", "core", "getCache", "gmCache", "ticker", "frame", "gMatrix", "ctm", "atan2", "sqrt", "cos", "getProperty", "ceil", "parseFloat", "force", "progress", "_interrupt", "onInterruptParams", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "update", "soft", "_this", "interrupt", "recordInlineStyles", "clear", "fit", "elStatesInOrder", "toElStates", "property", "es", "index", "es2", "compare", "place", "s1", "s2", "enter", "leave", "changed", "unchanged", "placeIfDoesNotExist", "s1Alt", "s2Alt", "l1", "l2", "a1", "timelines", "foundInProgress", "_this2", "updateVisibility", "makeAbsolute", "targetsAreElementStates", "_kill", "JSON", "stringify", "self", "nodeName", "_getID", "_recordProps", "trim", "FlipAction", "getStateById", "states", "FlipBatch", "config", "animate", "getState", "merge", "prevBatch", "prevAction", "_this3", "killConflicts", "Flip", "_this4", "once", "restart", "loadState", "done", "queue", "setState", "skipGetState", "_this5", "_killed", "stateOnly", "from", "to", "fromTo", "fromEl", "toEl", "runBackwards", "before", "after", "inlineProps", "ctx", "context", "arguments", "targetsOrStates", "killFlipsOf", "complete", "isFlipping", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isActive", "convertCoordinates", "fromElement", "toElement", "register", "document", "toArray", "getStyleSaver", "snap", "version", "registerPlugin"], "mappings": ";;;;;;;;;6MAcW,SAAVA,EAAUC,OACLC,EAAMD,EAAQE,eAAiBF,IAC7BG,KAAkBH,EAAQI,QAAU,gBAAiBJ,EAAQI,QAElEC,GADAF,EAAiB,eACuB,eAElCF,EAAIK,aAAeL,EAAMA,EAAIK,iBACpCC,EAAOC,OACPC,EAAkB,IAAIC,EAClBT,EAAK,CAERU,GADAC,EAAOX,GACWY,gBAClBC,EAAQb,EAAIc,MACZC,EAAOJ,EAAKK,gBAAgB,6BAA8B,MAErDb,MAAMc,UAAY,WAEnBC,EAAKlB,EAAImB,cAAc,OAC1BC,EAAKpB,EAAImB,cAAc,OACvBE,EAAOrB,IAAQA,EAAIc,MAAQd,EAAIsB,mBAC5BD,GAAQA,EAAKE,cAChBF,EAAKE,YAAYL,GACjBA,EAAGK,YAAYH,GACfF,EAAGM,aAAa,QAAS,kDACzBC,EAAiBL,EAAGM,eAAiBR,EACrCG,EAAKM,YAAYT,WAGZlB,EAoCW,SAAnB4B,WAAyBtB,EAAKuB,aAAgBlB,EAAKmB,WAAapB,EAAYoB,WAAajB,EAAMiB,WAAa,EACxF,SAApBC,WAA0BzB,EAAK0B,aAAerB,EAAKsB,YAAcvB,EAAYuB,YAAcpB,EAAMoB,YAAc,EACnG,SAAZC,EAAYnC,UAAWA,EAAQoC,kBAA6D,SAAxCpC,EAAQqC,QAAU,IAAIC,cAA0BtC,EAAU,MAU7F,SAAjBuC,EAAkBvC,EAASwC,MACtBxC,EAAQM,aAAeM,GAAQb,EAAQC,IAAW,KACjDyC,EAAMN,EAAUnC,GACnB0C,EAAKD,EAAOA,EAAIE,aAAa,UAAY,6BAAgC,+BACzEC,EAAOH,EAAOD,EAAI,OAAS,IAAO,MAClCK,EAAU,IAANL,EAAU,EAAI,IAClBM,EAAU,IAANN,EAAU,IAAM,EACpBO,EAAM,0EACNC,EAAIpC,EAAKK,gBAAkBL,EAAKK,gBAAgByB,EAAGO,QAAQ,SAAU,QAASL,GAAQhC,EAAKQ,cAAcwB,UACtGJ,IACEC,GAScS,EAAlBA,GAAkCX,EAAevC,GACjDgD,EAAEvB,aAAa,QAAS,KACxBuB,EAAEvB,aAAa,SAAU,KACzBuB,EAAEvB,aAAa,YAAa,aAAeoB,EAAI,IAAMC,EAAI,KACzDI,EAAc1B,YAAYwB,KAZrBG,KACJA,EAAgBZ,EAAevC,IACjBI,MAAMgD,QAAUL,GAE/BC,EAAE5C,MAAMgD,QAAUL,EAAM,gCAAkCD,EAAI,WAAaD,EAAI,KAC/EM,EAAc3B,YAAYwB,KAUrBA,OAEF,4BAUG,SAAVK,EAAUZ,OAERvB,EADGoC,EAAIb,EAAIc,gBAEPD,IACJpC,EAAYuB,EAAIrC,MAAMD,GACtBsC,EAAIrC,MAAMD,GAAkB,OAC5BsC,EAAIjB,YAAYR,GAChBsC,EAAItC,EAAKuC,SACTd,EAAIb,YAAYZ,GAChBE,EAAauB,EAAIrC,MAAMD,GAAkBe,EAAauB,EAAIrC,MAAMoD,eAAerD,EAAe8C,QAAQ,WAAY,OAAOX,gBAEnHgB,GAAK7C,EAAgBgD,QAEZ,SAAjBC,EAAkB1D,EAAS2D,OAMzBC,EAAWN,EAAGO,EAAGhB,EAAGC,EAAGgB,EALpBrB,EAAMN,EAAUnC,GACnB+D,EAAY/D,IAAYyC,EACxBuB,EAAWvB,EAAMwB,EAAYC,EAC7BC,EAASnE,EAAQM,WACjB8D,EAAaD,IAAW1B,GAAO0B,EAAOE,YAAcF,EAAOE,WAAW7C,YAAc2C,EAAOE,WAAaF,KAErGnE,IAAYO,SACRP,KAERgE,EAASM,QAAUN,EAASO,KAAKhC,EAAevC,EAAS,GAAIuC,EAAevC,EAAS,GAAIuC,EAAevC,EAAS,IACjH4D,EAAYnB,EAAMS,EAAgBC,EAC9BV,EACCsB,GAEHlB,IADAgB,EAAIR,EAAQrD,IACLgD,EAAIa,EAAEW,EACb1B,GAAKe,EAAEY,EAAIZ,EAAEa,EACbpB,EAAI7C,GACMT,EAAQ2E,SAClBd,EAAI7D,EAAQ2E,UAGZ9B,GADAS,GADAA,EAAItD,EAAQkB,UAAYlB,EAAQkB,UAAU0D,QAAU,IAC7CC,cAAoD,EAAlBvB,EAAEuB,cA1C/B,SAAfC,aAAexB,WACVyB,EAAI,IAAIrE,EACX8B,EAAI,EACEA,EAAIc,EAAEuB,cAAerC,IAC3BuC,EAAEC,SAAS1B,EAAE2B,QAAQzC,GAAG0C,eAElBH,EAoC0DD,CAAaxB,GAAKA,EAAE2B,QAAQ,GAAGC,OAAvEzE,GACjB+D,EAAIX,EAAEhB,EAAIS,EAAEyB,EAAIlB,EAAEf,EACxBA,EAAIQ,EAAEO,EAAIA,EAAEhB,EAAIS,EAAEoB,EAAIb,EAAEf,IAExBQ,EAAI,IAAI5C,EACRmC,EAAIC,EAAI,GAELa,GAAmD,MAAlC3D,EAAQqC,QAAQC,gBACpCO,EAAIC,EAAI,IAERiB,EAAYtB,EAAM0B,GAAQ3C,YAAYoC,GACvCA,EAAUnC,aAAa,YAAa,UAAY6B,EAAEkB,EAAI,IAAMlB,EAAEO,EAAI,IAAMP,EAAEyB,EAAI,IAAMzB,EAAEoB,EAAI,KAAOpB,EAAEN,EAAIH,GAAK,KAAOS,EAAEmB,EAAI3B,GAAK,SACxH,IACND,EAAIC,EAAI,EACJpB,MACH4B,EAAItD,EAAQ2B,aACZkC,EAAI7D,GACS6D,EAANA,GAAUA,EAAEvD,aAAeuD,IAAMP,GAAKO,EAAEvD,YACe,GAAxDC,EAAK4E,iBAAiBtB,GAAG1D,GAAkB,IAAImE,SACnDzB,EAAIgB,EAAEuB,WACNtC,EAAIe,EAAEwB,UACNxB,EAAI,MAKa,cADpBC,EAAKvD,EAAK4E,iBAAiBnF,IACpBsF,UAA2C,UAAhBxB,EAAGwB,aACpChC,EAAItD,EAAQ2B,aACLwC,GAAUA,IAAWb,GAC3BT,GAAKsB,EAAOjC,YAAc,EAC1BY,GAAKqB,EAAOpC,WAAa,EACzBoC,EAASA,EAAO7D,YAGlBuD,EAAID,EAAUxD,OACZmF,IAAOvF,EAAQqF,UAAYvC,EAAK,KAClCe,EAAE2B,KAAQxF,EAAQoF,WAAavC,EAAK,KACpCgB,EAAE1D,GAAkB2D,EAAG3D,GACvB0D,EAAExD,GAAwByD,EAAGzD,GAM7BwD,EAAEyB,SAA2B,UAAhBxB,EAAGwB,SAAuB,QAAU,WACjDlB,EAAW5C,YAAYoC,UAEjBA,EAEK,SAAb6B,EAAcnC,EAAGkB,EAAGX,EAAGkB,EAAGL,EAAG1B,EAAGyB,UAC/BnB,EAAEkB,EAAIA,EACNlB,EAAEO,EAAIA,EACNP,EAAEyB,EAAIA,EACNzB,EAAEoB,EAAIA,EACNpB,EAAEN,EAAIA,EACNM,EAAEmB,EAAIA,EACCnB,EAhNT,IAAI1C,EAAML,EAAMI,EAAaG,EAAOqC,EAAeD,EAAezC,EAAiBO,EAGlFU,IAFAvB,EAAiB,YACjBE,EAAuBF,EAAiB,SAgExC8D,EAAY,GACZC,EAAY,GAgJAxD,0BAKZgF,QAAA,uBACMlB,EAAoBmB,KAApBnB,EAAGX,EAAiB8B,KAAjB9B,EAAGkB,EAAcY,KAAdZ,EAAGL,EAAWiB,KAAXjB,EAAG1B,EAAQ2C,KAAR3C,EAAGyB,EAAKkB,KAALlB,EACnBmB,EAAepB,EAAIE,EAAIb,EAAIkB,GAAM,aAC3BU,EACNE,KACAjB,EAAIkB,GACH/B,EAAI+B,GACJb,EAAIa,EACLpB,EAAIoB,GACHb,EAAIN,EAAIC,EAAI1B,GAAK4C,IAChBpB,EAAIC,EAAIZ,EAAIb,GAAK4C,MAIrBZ,SAAA,kBAASE,OACHV,EAAoBmB,KAApBnB,EAAGX,EAAiB8B,KAAjB9B,EAAGkB,EAAcY,KAAdZ,EAAGL,EAAWiB,KAAXjB,EAAG1B,EAAQ2C,KAAR3C,EAAGyB,EAAKkB,KAALlB,EACnBoB,EAAKX,EAAOV,EACZsB,EAAKZ,EAAOH,EACZgB,EAAKb,EAAOrB,EACZxC,EAAK6D,EAAOR,EACZsB,EAAKd,EAAOlC,EACZiD,EAAKf,EAAOT,SACNgB,EAAWE,KACjBE,EAAKrB,EAAIuB,EAAKhB,EACdc,EAAKhC,EAAIkC,EAAKrB,EACdoB,EAAKtB,EAAInD,EAAK0D,EACde,EAAKjC,EAAIxC,EAAKqD,EACd1B,EAAIgD,EAAKxB,EAAIyB,EAAKlB,EAClBN,EAAIuB,EAAKnC,EAAIoC,EAAKvB,MAGpBjB,MAAA,wBACQ,IAAI/C,SAASiF,KAAKnB,EAAGmB,KAAK9B,EAAG8B,KAAKZ,EAAGY,KAAKjB,EAAGiB,KAAK3C,EAAG2C,KAAKlB,MAGlEyB,OAAA,gBAAOhB,OACDV,EAAoBmB,KAApBnB,EAAGX,EAAiB8B,KAAjB9B,EAAGkB,EAAcY,KAAdZ,EAAGL,EAAWiB,KAAXjB,EAAG1B,EAAQ2C,KAAR3C,EAAGyB,EAAKkB,KAALlB,SACZD,IAAMU,EAAOV,GAAKX,IAAMqB,EAAOrB,GAAKkB,IAAMG,EAAOH,GAAKL,IAAMQ,EAAOR,GAAK1B,IAAMkC,EAAOlC,GAAKyB,IAAMS,EAAOT,KAGhH0B,MAAA,eAAMC,EAAOC,YAAAA,IAAAA,EAAU,QACjBxD,EAAQuD,EAARvD,EAAGC,EAAKsD,EAALtD,EACN0B,EAAoBmB,KAApBnB,EAAGX,EAAiB8B,KAAjB9B,EAAGkB,EAAcY,KAAdZ,EAAGL,EAAWiB,KAAXjB,EAAG1B,EAAQ2C,KAAR3C,EAAGyB,EAAKkB,KAALlB,SACjB4B,EAAUxD,EAAKA,EAAI2B,EAAI1B,EAAIiC,EAAI/B,GAAM,EACrCqD,EAAUvD,EAAKD,EAAIgB,EAAIf,EAAI4B,EAAID,GAAM,EAC9B4B,+BAjDI7B,EAAKX,EAAKkB,EAAKL,EAAK1B,EAAKyB,YAAzBD,IAAAA,EAAE,YAAGX,IAAAA,EAAE,YAAGkB,IAAAA,EAAE,YAAGL,IAAAA,EAAE,YAAG1B,IAAAA,EAAE,YAAGyB,IAAAA,EAAE,GACtCgB,EAAWE,KAAMnB,EAAGX,EAAGkB,EAAGL,EAAG1B,EAAGyB,GA4D3B,SAAS6B,gBAAgBtG,EAAS0F,EAAS/B,EAAe4C,OAC3DvG,IAAYA,EAAQM,aAAeM,GAAQb,EAAQC,IAAUa,kBAAoBb,SAC9E,IAAIU,MAER8F,EAnPiB,SAArBC,mBAAqBzD,WAChBwB,EAAGkC,EACA1D,GAAKA,IAAMlC,IACjB4F,EAAQ1D,EAAE2D,QACDD,EAAME,SAAWF,EAAMG,IAAI7D,EAAG,KACnC0D,IAAUA,EAAMI,SAAWJ,EAAMK,QAAUL,EAAMM,kBACpDN,EAAMI,OAASJ,EAAMK,OAAS,KAC9BL,EAAMM,gBAAgB,EAAGN,GACzBlC,EAAIA,EAAED,KAAKmC,GAAUlC,EAAI,CAACkC,IAE3B1D,EAAIA,EAAE1C,kBAEAkE,EAuOSiC,CAAmBzG,GAEnCiH,EADM9E,EAAUnC,GACFiE,EAAYC,EAC1BN,EAAYF,EAAe1D,EAAS2D,GACpCuD,EAAKD,EAAM,GAAGE,wBACdrB,EAAKmB,EAAM,GAAGE,wBACdC,EAAKH,EAAM,GAAGE,wBACdhD,EAASP,EAAUtD,WACnB+G,GAAWd,GAtND,SAAXe,SAAWtH,SACsC,UAA5CO,EAAK4E,iBAAiBnF,GAASsF,YAGnCtF,EAAUA,EAAQM,aACkB,IAArBN,EAAQuH,SACfD,SAAStH,WAgNkBsH,CAAStH,GAC5CsD,EAAI,IAAI5C,GACNoF,EAAGN,KAAO0B,EAAG1B,MAAQ,KACrBM,EAAGP,IAAM2B,EAAG3B,KAAO,KACnB6B,EAAG5B,KAAO0B,EAAG1B,MAAQ,KACrB4B,EAAG7B,IAAM2B,EAAG3B,KAAO,IACpB2B,EAAG1B,MAAQ6B,EAAU,EAAIrF,KACzBkF,EAAG3B,KAAO8B,EAAU,EAAIxF,SAE1BsC,EAAOvC,YAAYgC,GACf4C,MACHU,EAAKV,EAAWlC,OACT4C,MACNpB,EAAKU,EAAWU,IACbJ,OAAShB,EAAGiB,OAAS,EACxBjB,EAAGkB,gBAAgB,EAAGlB,UAGjBJ,EAAUpC,EAAEoC,UAAYpC,EC3Sf,SAAhBkE,EAAiBC,EAAOC,UAASD,EAAME,QAAQC,QAAQ,SAAApD,UAAKA,EAAEqD,KAAKH,IAASlD,EAAEqD,KAAKH,GAAMlD,KAO1E,SAAfsD,EAAeC,SAAyB,iBAAVA,EAAqBA,EAAKC,MAAM,KAAKC,KAAK,IAAID,MAAM,KAAOD,EAGhF,SAATG,EAASC,UAAUC,EAASD,GAAQ,IAAME,QAAQC,KAAK,qBAAsBH,GACpE,SAATI,EAASC,UAASC,KAAKC,MAAc,IAARF,GAAiB,KAAS,EACxC,SAAfG,EAAgBC,EAASC,EAAWC,UAAWF,EAAQhB,QAAQ,SAAAmB,UAAMA,EAAGC,UAAUF,GAAQD,KAGzE,SAAjBI,EAAiBC,UAAKA,EAAEjG,QAAQ,WAAY,OAAOX,cAC3C,SAAR6G,EAASC,EAAKC,OACIH,EAAbI,EAAS,OACRJ,KAAKE,EACTC,EAAQH,KAAOI,EAAOJ,GAAKE,EAAIF,WAEzBI,EAGQ,SAAhBC,GAAgBC,OACXN,EAAIO,GAAeD,GAAS1B,EAAa0B,UAC7CE,GAAqBF,GAASN,EAAES,OAAOC,IAChCV,EAoBW,SAAnBW,GAAoBC,EAAOC,EAAQC,UAClCF,EAAMlC,QAAQ,SAAAqC,UAAQA,EAAKvF,EAXb,SAAfwF,aAAgBnB,EAAIgB,EAAQI,YAAAA,IAAAA,EAAQ,WAC/BhG,EAAS4E,EAAGzI,WACf8J,EAAM,aAAQ,GAAMD,IAAUJ,GAAU,EAAI,GAC5CM,EAAIN,EAAgB,KAANK,EAAY,EACpBrB,GACNsB,GAAKD,EACLrB,EAAKA,EAAGuB,uBAEFnG,EAASkG,EAAIH,aAAa/F,EAAQ4F,EAAQI,EAAQ,GAAKE,EAG/BH,CAAaF,EAAaC,EAAKjK,QAAUiK,EAAKM,EAAGR,KAChFD,EAAMU,KAAK,SAACC,EAAI1E,UAAO0E,EAAG/F,EAAIqB,EAAGrB,IAC1BoF,EAEc,SAAtBY,GAAuBC,EAASnB,WAI9BN,EAAG0B,EAHAxK,EAAQuK,EAAQ3K,QAAQI,MAC3BoE,EAAImG,EAAQ5H,IAAM4H,EAAQ5H,KAAO,GACjCP,EAAIgH,EAAMlF,OAEJ9B,KAENoI,EAAIxK,EADJ8I,EAAIM,EAAMhH,KACMpC,EAAMyK,iBAAiB3B,GACvC1E,EAAED,KAAKqG,EAAI1B,EAAI4B,EAAkB5B,KAAO4B,EAAkB5B,GAAKD,EAAeC,IAAK0B,UAE7ExK,EAEa,SAArB2K,GAAqBC,OAChBjI,EAAMiI,EAAMjI,IACf3C,EAAQ4K,EAAMhL,QAAQI,MACtBoC,EAAI,MACLwI,EAAMtE,MAAME,QAAU,EACfpE,EAAIO,EAAIuB,OAAQ9B,GAAG,EACzBO,EAAIP,EAAE,GAAMpC,EAAM2C,EAAIP,IAAMO,EAAIP,EAAE,GAAMpC,EAAMoD,eAAeT,EAAIP,KAE7DO,EAAIA,EAAIkI,QAAQ,aAAa,IAAM7K,EAAM8K,YAC7C9K,EAAMoD,eAAe,aACrBpD,EAAMoD,eAAe,SACrBpD,EAAMoD,eAAe,WAGL,SAAlB2H,GAAmBrB,EAAOsB,GACzBtB,EAAMlC,QAAQ,SAAA7C,UAAKA,EAAEP,EAAEkC,MAAME,QAAU,IACvCwE,GAAkBtB,EAAMuB,YAAYzD,QAAQmD,IAG7B,SAAhBO,GAAiBX,EAASY,EAAcC,OAItClC,EAAQmC,EAAe3H,EAHlB9D,EAA6C2K,EAA7C3K,QAAS0L,EAAoCf,EAApCe,MAAOC,EAA6BhB,EAA7BgB,OAAQ/E,EAAqB+D,EAArB/D,QAASgF,EAAYjB,EAAZiB,QACtCxL,EAAQJ,EAAQI,MAChBoC,EAAI,KAEqB,iBAAlB+I,IAAgCA,EAAeZ,GACnDkB,IAA0B,IAAhBL,SACbK,GAAOC,KAAKvH,KAAK,CAACgG,EAAGvK,EAAS6D,EAAG8G,EAASnG,EAAGmG,EAASoB,GAAI,IAC1DF,GAAOG,OAAOzH,KAAK,kBAAOoG,EAAQjE,MAAME,QAAU,IAAMmE,GAAmBJ,KACpE3K,MAERyL,EAAuC,SAAvBG,EAAQ,WAEnBjB,EAAQsB,YAAaR,IACzBA,IAAkBf,GAAoBC,EAAS,CAAC,YAAYuB,QAAUX,EAAaW,SACnFvB,EAAQzF,OAASqG,EAAarG,OAC9ByF,EAAQe,MAAQA,EAAQf,EAAQe,OAASH,EAAaG,MACtDf,EAAQgB,OAASA,EAAShB,EAAQgB,QAAUJ,EAAaI,QAG1DjB,GAAoBC,EAASwB,GAC7BrI,EAAKtD,OAAO2E,iBAAiBnF,GACtBwC,KACNpC,EAAM+L,EAAe3J,IAAMsB,EAAGqI,EAAe3J,OAE9CpC,EAAMgM,SAAW,gBACjBhM,EAAMiM,WAAa,OAEnBjM,EAAMkF,SAAW,WACjBlF,EAAMsL,MAAQA,EAAQ,KACtBtL,EAAMuL,OAASA,EAAS,KACxBvL,EAAMmF,MAAQnF,EAAMmF,IAAM,OAC1BnF,EAAMoF,OAASpF,EAAMoF,KAAO,OACxBoB,EACH0C,EAAS,IAAIgD,GAAatM,YAE1BsJ,EAASH,EAAMwB,EAAS4B,IACjBjH,SAAW,WACdqF,EAAQ6B,OAAQ,KACfC,EAASzM,EAAQmH,wBACrBmC,EAAOpE,OAAS,IAAIxE,EAAS,EAAG,EAAG,EAAG,EAAG+L,EAAOjH,KAAOxD,IAAqByK,EAAOlH,IAAM1D,UAEzFyH,EAAOpE,OAASoB,gBAAgBtG,GAAS,GAAO,GAAO,UAGzDsJ,EAASoD,GAAKpD,EAAQqB,GAAS,GAC/BA,EAAQ9H,EAAI8J,EAAcrD,EAAOzG,EAAG,KACpC8H,EAAQ7H,EAAI6J,EAAcrD,EAAOxG,EAAG,KAC7B9C,EAEO,SAAf4M,GAAgB9C,EAAOlB,UACN,IAAZA,IACHA,EAAUR,EAASQ,GACnBkB,EAAQA,EAAM+C,OAAO,SAAA9H,OACqC,IAArD6D,EAAQqC,SAASlG,EAAEgH,GAAK,EAAIhH,EAAElB,EAAIkB,EAAEP,GAAGxE,gBAChC,EAEV+E,EAAEwF,EAAE5D,MAAMK,gBAAgB,GACtBjC,EAAElB,EAAEoI,YACPlH,EAAEwF,EAAEnK,MAAMsL,MAAQ3G,EAAElB,EAAE6H,MAAQ,KAC9B3G,EAAEwF,EAAEnK,MAAMuL,OAAS5G,EAAElB,EAAE8H,OAAS,SAK7B7B,EAEa,SAArBgD,GAAqBhD,UAASD,GAAiBC,GAAO,GAAMlC,QAAQ,SAAA7C,UAAMA,EAAEP,EAAEyH,WAAalH,EAAElB,EAAEoI,YAAcX,GAAcvG,EAAEgH,GAAK,EAAIhH,EAAElB,EAAIkB,EAAEP,EAAGO,EAAElB,EAAG,KAaxI,SAAdkJ,GAAe/M,EAASwJ,OAEtBN,EADG9I,EAAQJ,EAAQI,OAASJ,MAExBkJ,KAAKM,EACTpJ,EAAM8I,GAAKM,EAAMN,GAQU,SAA7B8D,GAA6BC,UAAYA,EAASC,IAAI,SAAAvC,UAAWA,EAAQ3K,UACvD,SAAlBmN,GAAmBC,EAAUH,EAAUI,UAAOD,GAAYH,EAAS3I,QAAU+I,EAAGC,IAAIF,EAASJ,GAA2BC,GAAWI,EAAI,IAAIE,GAAUN,EAAU,GAAG,IAAQ,GAuF5J,SAAdO,GAAeC,EAAgB5F,UAAS4F,aAA0BF,GAAYE,EAAiB,IAAIF,GAAUE,EAAgB5F,GACvG,SAAtB6F,GAAuBC,EAASC,EAAWC,OACtCC,EAAMH,EAAQI,SAASF,GAC1BG,EAAML,EAAQM,IAAIJ,UACZG,EAAI/B,YAAgB2B,EAAUM,gBAAgBF,EAAIhO,UAAYgO,GAAK/B,WAAc6B,EAAI7B,UAAmB6B,EAANE,EAGxF,SAAlBG,GAAkBC,MACbA,IAASC,EAAa,KACrBC,EAAIxN,EAAMV,MACbmO,EAAIzN,EAAM0N,cAAgBhO,OAAOiO,WACjCC,EAAI5N,EAAM6N,eAAiBnO,OAAOoO,YAClCpM,EAAI,KACD4L,IAASG,GAAKG,GAAI,MACdlM,KACNqM,EAAarM,GAAK8L,EAAEQ,EAAWtM,IAE5B+L,IACHD,EAAE5C,MAAQ5K,EAAM0N,YAAc,KAC9BF,EAAES,UAAY,UAEXL,IACHJ,EAAE3C,OAAS7K,EAAM6N,aAAe,KAChCL,EAAEU,UAAY,UAEfX,EAAaD,OACP,GAAIC,EAAa,MAChB7L,KACNqM,EAAarM,GAAM8L,EAAEQ,EAAWtM,IAAMqM,EAAarM,GAAM8L,EAAE9K,eAAeyF,EAAe6F,EAAWtM,KAErG6L,EAAcD,IAKP,SAAVa,GAAWrB,EAAWD,EAAS9F,EAAMqH,GACnCtB,aAAqBL,IAAaI,aAAmBJ,IAAclF,QAAQC,KAAK,iCAehFsC,EAAG1B,EAAGiG,EAAS3M,EAAGuG,EAAIkB,EAAMe,EAAOpC,EAASyC,EAAa+D,EAAUC,EAAQC,EAAK9K,EAAGX,IAb9E0L,GADN1H,EAAOA,GAAQ,IACT0H,WAAYC,EAAkK3H,EAAlK2H,QAASC,EAAyJ5H,EAAzJ4H,QAASC,EAAgJ7H,EAAhJ6H,SAAUC,EAAsI9H,EAAtI8H,gBAAiBC,EAAqH/H,EAArH+H,OAAQC,EAA6GhI,EAA7GgI,MAAOC,EAAsGjI,EAAtGiI,OAAQC,EAA8FlI,EAA9FkI,OAAQC,EAAsFnI,EAAtFmI,YAAaC,EAAyEpI,EAAzEoI,KAAMC,EAAmErI,EAAnEqI,YAAaC,EAAsDtI,EAAtDsI,OAAQC,EAA8CvI,EAA9CuI,OAAQC,EAAsCxI,EAAtCwI,MAAOC,EAA+BzI,EAA/ByI,KAAMC,EAAyB1I,EAAzB0I,QAASC,EAAgB3I,EAAhB2I,KAAMC,EAAU5I,EAAV4I,MACzKjH,GAAS,UAAW3B,EAAOA,EAAO+F,GAAWpE,MAC7CkH,EAAYvH,EAAMtB,EAAM8I,IACxBC,EAAYC,EAAKC,SAAS,CAAEjB,MAAAA,EAAOC,OAAAA,EAAQC,OAAAA,EAAQC,YAAAA,EAAaC,KAAAA,EAAMc,KAAM,WAC5EC,EAAiBN,EACjBO,EAAW,GACXC,EAAU,GACVpH,EAAQ,GACRqH,EAAiB,GACjBC,GAAmB,IAATZ,EAAgB,EAAIA,GAAQ,EACtCa,EAA4B,mBAAVb,EAAuBA,EAAO,kBAAMY,GACtDE,EAAc1D,EAAU0D,aAAe3D,EAAQ2D,YAC/CC,EAAUX,EAAuB,IAAb1B,EAAiB,KAAO,YAGxChG,KAAKyE,EAAQI,SACjBsB,EAAU1B,EAAQM,IAAI/E,GAA2BwE,GAAoBC,EAASC,EAAW1E,GAA9DyE,EAAQI,SAAS7E,GAC5CH,EAAKsG,EAAOrP,QACZoP,EAAWxB,EAAUG,SAAS7E,IAC9B0E,EAAUK,IAAI/E,IAAMH,IAAOqG,EAASpP,UAAY4N,EAAUK,IAAI/E,GAAG+C,WAAcoD,EAAOpD,YAAemD,EAAWxB,EAAUK,IAAI/E,IAC1HkG,GACHnF,EAAO,CAACM,EAAGxB,EAAIlF,EAAGuL,EAAU5K,EAAG6K,EAAQtD,GAAIqD,EAASpP,UAAY+I,EAAK,EAAIsG,EAAOpD,UAAY,GAAK,GACjGnC,EAAMvF,KAAK0F,GACPA,EAAK8B,KACJ9B,EAAK8B,GAAK,IACb9B,EAAKpG,EAAIwL,EACTpF,EAAKzF,EAAI4K,GAGVkC,GAAe5G,GAAoBT,EAAKpG,EAAG2F,EAAQE,GAAqBF,GAASI,IACjF0G,GAAQxG,EAAMvF,KAAK0F,EAAKuH,KAAO,CAACjH,EAAG6E,EAASpP,QAAS6D,EAAGoG,EAAKpG,EAAGW,EAAGyF,EAAKzF,EAAGuH,IAAK9B,EAAK8B,GAAIyF,KAAMvH,KAEhGlB,EAAG0I,MAAQrC,EAASpP,QAAQyR,MAAQ5F,GAASA,GAAOiF,SAAWF,GACrDvB,EAAOpD,YACjBnC,EAAMvF,KAAK,CAACgG,EAAGxB,EAAIlF,EAAGsF,EAAMkG,EAAQ,CAACpD,UAAU,IAAKzH,EAAG6K,EAAQtD,GAAI,EAAGkF,SAAU,IAChFlI,EAAG0I,MAAQ5F,GAASA,GAAOiF,SAAWF,GAIxCpH,IAAUC,GAAeD,IAAUD,GAAcC,IAAQ5B,QAAQ,SAAAsB,UAAKwH,EAAUxH,GAAK,SAAA1G,UAAKsH,EAAMtH,GAAGgC,EAAEgF,MAAMN,MAC3GY,EAAMuB,YAAcA,EAAc,GAElCiE,EAAM,mBACLzF,GAAiBC,GACjBqE,IAAgB,GAEX3L,EAAI,EAAGA,EAAIsH,EAAMxF,OAAQ9B,IAC7ByH,EAAOH,EAAMtH,GACbgC,EAAIyF,EAAKzF,EACTX,EAAIoG,EAAKpG,GACL4M,GAAUjM,EAAEkN,YAAY7N,IAAOoG,EAAKgH,UAGvClI,EAAKkB,EAAKM,GACV4F,GAAYlG,EAAK8B,GAAK,IAAMvJ,IAAMgC,EAAEU,OAASoB,gBAAgByC,GAAI,GAAO,GAAO,IAC3ElF,EAAEoI,WAAazH,EAAEyH,WAChBhC,EAAK8B,GAAK,GACbf,EAAQ,IAAIsB,GAAavD,EAAIS,EAAOoE,EAAUpB,QAC9CE,GAAK1B,EAAOxG,EAAG6L,EAAO,EAAG,EAAGrF,GAC5BA,EAAM9F,OAASoB,gBAAgByC,GAAI,GAAO,GAAO,GACjDiC,EAAMjI,IAAMkH,EAAKpG,EAAEd,IACnBkH,EAAKzF,EAAIA,EAAIwG,EACbsF,IAASvH,EAAG3I,MAAMuR,QAAUL,EAAczN,EAAE8N,QAAUnN,EAAEmN,SACxDpB,GAAWY,EAAe5M,KAAKwE,IACX,EAAVkB,EAAK8B,IAAUuE,IACzBvH,EAAG3I,MAAMuR,QAAUL,EAAc9M,EAAEmN,QAAU9N,EAAE8N,QAAU,KAE1DjF,GAAKlI,EAAGX,EAAGwM,EAAO7G,IAER3F,EAAEoI,YAAczH,EAAEyH,YACvBpI,EAAEoI,UAGKzH,EAAEyH,YACbpI,EAAEd,IAAMyB,EAAEzB,IACVmO,EAAQ3M,KAAKV,GACbiG,EAAM8H,OAAOpP,IAAK,GAClBkN,GAAYS,GAAUzD,GAAKlI,EAAGX,EAAGwM,EAAO7G,KANxChF,EAAEyH,WAAagF,EAAS1M,KAAKC,GAC7BsF,EAAM8H,OAAOpP,IAAK,KAQf6N,IACJtH,EAAG3I,MAAMyR,SAAWpJ,KAAKqJ,IAAItN,EAAEkH,MAAO7H,EAAE6H,OAAS,KACjD3C,EAAG3I,MAAM2R,UAAYtJ,KAAKqJ,IAAItN,EAAEmH,OAAQ9H,EAAE8H,QAAU,KACpD5C,EAAG3I,MAAM4R,SAAWvJ,KAAKwJ,IAAIzN,EAAEkH,MAAO7H,EAAE6H,OAAS,KACjD3C,EAAG3I,MAAM8R,UAAYzJ,KAAKwJ,IAAIzN,EAAEmH,OAAQ9H,EAAE8H,QAAU,MAErDwE,GAAUD,GAAenH,EAAGC,UAAUsE,IAAI4C,IAnC1CpG,EAAM8H,OAAOpP,IAAK,GAqCnB6I,EAAY9G,KAAKC,OAEd2N,KACAjC,IACHiC,EAAe9G,EAAY6B,IAAI,SAAAoB,UAAKA,EAAEtO,UACtCmQ,GAAUgC,EAAavK,QAAQ,SAAA5E,UAAKA,EAAEgG,UAAUoJ,OAAOlC,MAGxD/B,IAAgB,GAEZkC,GACHK,EAAU5J,OAAS,SAAAtE,UAAKsH,EAAMtH,GAAGgC,EAAEsC,QACnC4J,EAAU3J,OAAS,SAAAvE,UAAKsH,EAAMtH,GAAGgC,EAAEuC,UAEnC2J,EAAUhF,MAAQ,SAAAlJ,UAAKsH,EAAMtH,GAAGgC,EAAEkH,MAAQ,MAC1CgF,EAAU/E,OAAS,SAAAnJ,UAAKsH,EAAMtH,GAAGgC,EAAEmH,OAAS,MAC5C+E,EAAU2B,UAAYxK,EAAKwK,YAAa,GAEzC3B,EAAU7N,EAAI,SAAAL,UAAKsH,EAAMtH,GAAGgC,EAAE3B,EAAI,MAClC6N,EAAU5N,EAAI,SAAAN,UAAKsH,EAAMtH,GAAGgC,EAAE1B,EAAI,MAClC4N,EAAU4B,SAAW,SAAA9P,UAAKsH,EAAMtH,GAAGgC,EAAE8N,UAAY9B,EAA0C,IAAnCa,EAAS7O,EAAGoG,EAAQpG,GAAIoG,GAAiB,IACjG8H,EAAU6B,MAAQ,SAAA/P,UAAKsH,EAAMtH,GAAGgC,EAAE+N,OAElC3J,EAAUkB,EAAMoD,IAAI,SAAAnI,UAAKA,EAAEwF,KAEvB6F,GAAqB,IAAXA,IACbM,EAAU8B,UAAY,CAACpC,OAAQ,yBAAMA,IACrCM,EAAUN,OAASA,EACnBM,EAAU+B,iBAA2C,IAAzB5K,EAAK4K,iBAGlCnC,IAASI,EAAUiB,QAAU,SAAAnP,UAAKsH,EAAMtH,GAAGuJ,GAAK,EAAI,EAAkB,EAAdjC,EAAMtH,GAAGuJ,GAASjC,EAAMtH,GAAGgC,EAAEmN,QAAU,QAE3FR,EAAe7M,OAAQ,CAC1BiM,EAAUM,EAAK6B,MAAMC,WAAWpC,OAC5BqC,EAAahK,EAAQiK,MAAM1B,EAAe7M,QAC9CoM,EAAUH,QAAU,SAAC/N,EAAGuG,UAAOwH,GAASY,EAAelG,QAAQlC,GAAMH,EAAQqC,QAAQnB,EAAMtH,GAAGgP,KAAKjH,GAAK/H,EAAGuG,EAAI6J,OAUhHE,GAAWlL,QAAQ,SAAAF,UAAQG,EAAKH,IAASkJ,EAAUmC,cAAcrL,EAAMG,EAAKH,GAAOG,EAAKH,EAAO,aAE3FkI,GAAUhH,EAAQtE,WAMhB4E,KALL8H,EAAiB7H,EAAMuH,EAAWC,IAC9B,UAAWf,IACdA,EAAO9I,OAAS8I,EAAO7I,OAAS6I,EAAOS,aAChCT,EAAOS,OAELT,GACThF,EAAIzB,EAAMyG,EAAO1G,GAAI8J,KACnB9J,GAAKwH,EAAUxH,KACf,aAAc0B,IAAO,aAAc8F,IAAe9F,EAAEqI,SAAWvC,EAAUuC,UAC3ErI,EAAE2F,QAAUG,EAAUH,QACtBgB,EAAQ2B,KAAKtC,EAAWhI,EAASgC,EAAG,UAC7BoG,EAAe9H,IAGpBN,EAAQtE,QAAU4M,EAAQ5M,QAAU2M,EAAS3M,UAChD4L,GAAeU,EAAUtD,IAAI,kBAAM3E,EAAawJ,EAAcjC,EAAaU,EAAUuC,OAAS,EAAI,SAAW,QAAQ,KAAOrD,GAAUnH,EAAawJ,EAAcjC,EAAa,OAC9KtH,EAAQtE,QAAUiN,EAAQ2B,KAAKtC,EAAWhI,EAASoI,EAAgB,IAGpE7D,GAAgBqC,EAASyB,EAAUL,GACnCzD,GAAgBsC,EAASyB,EAASN,OAE9BwC,EAAUvH,IAAUA,GAAOiF,SAE3BsC,IACHA,EAAQ9F,IAAIsD,EAAW,GACvB/E,GAAOG,OAAOzH,KAAK,kBAAM4G,GAAgBrB,GAAQyF,MAGlDJ,EAAUyB,EAAUqC,WACpBrC,EAAUsC,KAAK,eACVG,EAAUzC,EAAU0C,QAAUnE,EAClCkE,IAAYD,GAAWjI,GAAgBrB,GAAQyF,GAC/CW,GAAevH,EAAawJ,EAAcjC,EAAamD,EAAU,SAAW,UAI9E1D,IAAoBD,EAAW5F,EAAM+C,OAAO,SAAA5C,UAASA,EAAK8B,KAAO9B,EAAKzF,EAAEyH,WAAahC,EAAKpG,EAAEoI,YAAWiB,IAAI,SAAAjD,UAAQA,EAAKzF,EAAExE,WACtH6L,IACH6D,MAAY7D,GAAOC,MAAKvH,aAAQqI,GAAa9C,EAAO4F,IACpD7D,GAAO0H,KAAKhP,KAAK+K,KAEjBI,GAAY5C,GAAmBF,GAAa9C,EAAO4F,IACnDJ,SAGGkE,EAAO3H,GAASA,GAAOiF,SAAWF,SACtC4C,EAAKC,OAAS,kBAAMC,GAAUF,EAAM,EAAG,IAEhCA,EAgBQ,SAAhBG,GAAgB3I,WAKdL,EAJGiJ,EAAS5I,EAAM+C,SAAW,GAC7BE,EAAMjD,EAAMiD,IAAM,GAClBhB,EAAWjC,EAAM6I,cACjBrR,EAAIyK,EAAS3I,OAEP9B,KAENoR,GADAjJ,EAAUsC,EAASzK,IACJqL,IAAOI,EAAItD,EAAQkD,IAAMlD,EAAYiJ,EAAOjJ,EAAQkD,IAAMlD,EAjgB5E,IACCvC,EAAUyI,EAAMhF,GAAQiI,EAAchT,EAAO6L,EAAeoH,EA8QmB1F,IA/Q5E2F,EAAM,EAGTC,EAAe,GACfC,EAAW,IAAMzL,KAAK0L,GACtBC,EAAW3L,KAAK0L,GAAK,IACrB5H,EAAY,GACZzB,EAAoB,GACpBpB,GAAuB,GAEvBoJ,GAAahL,EAAa,6DAC1B8B,GAAe9B,EAAa,iHAI5B6I,GAAY,CAACP,OAAO,EAAGiE,KAAK,EAAG7H,OAAO,EAAGgE,KAAK,EAAGjB,WAAW,EAAG3G,QAAQ,EAAGsH,YAAY,EAAGoE,WAAW,EAAGC,SAAS,EAAGC,YAAY,EAAGC,QAAQ,EAAG5E,MAAM,EAAGE,OAAO,EAAGC,YAAY,EAAGC,KAAK,EAAGI,MAAM,EAAGC,KAAK,EAAGZ,SAAS,EAAGlG,MAAM,EAAGgG,QAAQ,EAAGC,QAAQ,EAAGG,OAAO,EAAGE,OAAO,EAAGK,OAAO,EAAGM,MAAM,EAAGd,gBAAiB,GAC3SqD,GAAe,CAAC5C,OAAO,EAAG5D,OAAO,EAAG+C,WAAW,EAAGc,MAAM,EAAGX,SAAS,EAAGgF,SAAS,EAAGC,QAAQ,EAAGnL,MAAM,GASpGC,GAAiB,GA2DjB0C,EAAiB,wEAAwEnE,MAAM,KAsE/F4M,EAAqB,SAArBA,mBAAsBC,EAAUrL,EAAOgD,EAAQsI,UAAUD,aAAoBvI,GAAeuI,EAAWA,aAAoBtH,GADrG,SAAtBwH,oBAAuB/J,EAAO8J,UAAWA,GAAS9J,EAAM+C,SAAS6G,EAAmBE,GAAOjH,KAAQ7C,EAAM6I,cAAc,GACgBkB,CAAoBF,EAAUC,GAAS,IAAIxI,GAAkC,iBAAduI,EAAyB3M,EAAO2M,IAAaxM,QAAQC,KAAKuM,EAAW,cAAgBA,EAAUrL,EAAOgD,IA0B5SE,GAAO,SAAPA,KAAQkB,EAAWD,EAAS0C,EAAO2E,EAAYN,EAAU7M,OAQvD0K,EAAO0C,EAAWC,EAAStJ,EAASuJ,EAAcjQ,EAAQkQ,EAPrDpV,EAAiC4N,EAAjC5N,QAAS0G,EAAwBkH,EAAxBlH,MAAOvC,EAAiByJ,EAAjBzJ,OAAQtB,EAAS+K,EAAT/K,EAAGC,EAAM8K,EAAN9K,EAC9B4I,EAAoDiC,EAApDjC,MAAOC,EAA6CgC,EAA7ChC,OAAQ7E,EAAqC6G,EAArC7G,OAAQC,EAA6B4G,EAA7B5G,OAAQuL,EAAqB3E,EAArB2E,SAAU7F,EAAWkB,EAAXlB,OAC3C4I,EAASxN,GAAQkM,GAAkBA,EAAe/T,EAAS,0BAC3DsV,EAAiB1H,IACRD,EAAQzI,OAAhBlC,IAAAA,EAAGyB,IAAAA,EACJ8Q,EAAO3H,EAAUnB,OAAOf,QAAUe,EAAOf,OAASkC,EAAUnB,OAAOd,SAAWc,EAAOd,QAAUiC,EAAU9G,SAAWA,GAAU8G,EAAU7G,SAAWA,GAAU6G,EAAU0E,WAAaA,EACpL9F,GAAU+I,GAAQ3H,EAAUpB,QAAUmB,EAAQnB,SAAWkI,SAEtDlI,IAAWrI,GACd2C,EAASC,EAAS,EAClBuL,EAAWC,EAAQ,IAGnBrN,GADAiQ,EAlKwB,SAA1BK,wBAA0BzM,OACrBrC,EAAQqC,EAAGpC,OAASkK,EAAK4E,KAAKC,SAAS3M,UACvCrC,EAAMiP,UAAY9E,EAAK+E,OAAOC,MAC1BnP,EAAMoP,SAEdpP,EAAMiP,QAAU9E,EAAK+E,OAAOC,MACpBnP,EAAMoP,QAAUxP,gBAAgByC,GAAI,GAAM,GAAO,IA4JzCyM,CAAwBrR,IACjBV,QAAQuB,SAAS2I,EAAQoI,IAAMpI,EAAQzI,OAAOzB,QAAQuB,SAAS2I,EAAQoI,KAAOpI,EAAQzI,QAC5GoN,EAAW/J,EAAOE,KAAKuN,MAAM9Q,EAAOrB,EAAGqB,EAAOV,GAAK0P,GACnD3B,EAAQhK,EAAOE,KAAKuN,MAAM9Q,EAAOH,EAAGG,EAAOR,GAAKwP,EAAW5B,GAAY,IACvExL,EAAS2B,KAAKwN,KAAKxN,SAAAvD,EAAOV,EAAK,YAAIU,EAAOrB,EAAK,IAC/CkD,EAAS0B,KAAKwN,KAAKxN,SAAAvD,EAAOH,EAAK,YAAIG,EAAOR,EAAK,IAAK+D,KAAKyN,IAAI3D,EAAQ6B,GACjEM,IACHA,EAAWtM,EAASsM,GAAU,GAC9B9I,EAAUiF,EAAKsF,YAAYzB,GAC3BU,EAAOV,EAAS/P,SAAwC,mBAAtB+P,EAAS/P,SAA2B+P,EAAS/P,UAC/E2Q,EAAiB,CAACxO,OAAQ8E,EAAQ,UAAW7E,OAAQ6E,EAAQ,UAAWF,MAAO0J,EAAOA,EAAK1J,MAAQjD,KAAK2N,KAAKC,WAAWzK,EAAQ,QAAS,QAASD,OAAQyJ,EAAOA,EAAKzJ,OAAS0K,WAAWzK,EAAQ,SAAU,SAE7MlF,EAAM4L,SAAWA,EAAW,MAC5B5L,EAAM6L,MAAQA,EAAQ,OAEnBlC,GACHvJ,GAAU4E,IAAU4J,EAAe5J,OAAU4J,EAAe5J,MAAYA,EAAQ4J,EAAe5J,MAA3B,EACpE3E,GAAU4E,IAAW2J,EAAe3J,QAAW2J,EAAe3J,OAAaA,EAAS2J,EAAe3J,OAA5B,EACvEjF,EAAMI,OAASA,EACfJ,EAAMK,OAASA,IAEf2E,EAAQiB,EAAcjB,EAAQ5E,EAASwO,EAAexO,OAAQ,GAC9D6E,EAASgB,EAAchB,EAAS5E,EAASuO,EAAevO,OAAQ,GAChE/G,EAAQI,MAAMsL,MAAQA,EAAQ,KAC9B1L,EAAQI,MAAMuL,OAASA,EAAS,MAMjCqJ,GAAcjI,GAAY/M,EAAS2N,EAAQnE,OACvCgD,IAAWrI,GACdtB,GAAKG,EAAI4K,EAAU1I,OAAOlC,EAC1BF,GAAK2B,EAAImJ,EAAU1I,OAAOT,GAChB8Q,GAAQpR,IAAWwJ,EAAQxJ,QACrCuC,EAAMM,gBAAgB,EAAGN,GACzBxB,EAASoB,gBAAgBoO,GAAY1U,GAAS,GAAO,GAAO,GAC5DiV,EAAYE,EAAahP,MAAM,CAACtD,EAAGqC,EAAOlC,EAAGF,EAAGoC,EAAOT,IAEvD5B,IADAqS,EAAUC,EAAahP,MAAM,CAACtD,EAAGG,EAAGF,EAAG2B,KAC1B5B,EAAIoS,EAAUpS,EAC3BC,GAAKoS,EAAQpS,EAAImS,EAAUnS,IAE3BqS,EAAanS,EAAImS,EAAa1Q,EAAI,EAElC5B,IADAqS,EAAUC,EAAahP,MAAM,CAACtD,EAAGG,EAAI4K,EAAU1I,OAAOlC,EAAGF,EAAG2B,EAAImJ,EAAU1I,OAAOT,KACpE5B,EACbC,GAAKoS,EAAQpS,GAEdD,EAAI8J,EAAc9J,EAAG,KACrBC,EAAI6J,EAAc7J,EAAG,MACjB+E,GAAUA,aAAgByE,IAG7B5F,EAAM7D,EAAIA,EAAI,KACd6D,EAAM5D,EAAIA,EAAI,KACd4D,EAAMM,gBAAgB,EAAGN,IAJzB2O,GAAUA,EAAO5B,SAMd5L,IACHA,EAAKhF,EAAIA,EACTgF,EAAK/E,EAAIA,EACT+E,EAAKyK,SAAWA,EAChBzK,EAAK0K,MAAQA,EACTlC,GACHxI,EAAKf,OAASA,EACde,EAAKd,OAASA,IAEdc,EAAK6D,MAAQA,EACb7D,EAAK8D,OAASA,IAGT9D,GAAQnB,GAShBmI,EAAe,GAAIC,EAAa,mCAAmC9G,MAAM,KAgOzE0L,GAAY,SAAZA,UAAarG,EAAIvE,EAAQwN,MACpBjJ,GAAMA,EAAGkJ,WAAa,KAAOlJ,EAAGyC,UAAYwG,UAC3CxN,IANO,SAAb0N,WAAanJ,GACZA,EAAGxF,KAAK2M,aAAenH,EAAGxF,KAAK2M,YAAYrO,MAAMkH,EAAIA,EAAGxF,KAAK4O,mBAAqB,IAClFpJ,EAAGqJ,aAAY,GAAM,GAAO,GAAM9O,QAAQ4O,YAKxCA,CAAWnJ,GACXvE,EAAS,GAAKuE,EAAGkJ,SAAS,GAC1BlJ,EAAGgH,SAEG,GAoBJ9G,4BAiBLoJ,OAAA,gBAAOC,0BACD/C,cAAgBlO,KAAKiD,QAAQsE,IAAI,SAAAnE,UAAM,IAAIuD,GAAavD,EAAI8N,EAAKrN,MAAOqN,EAAKrK,UAClFmH,GAAchO,WACTmR,UAAUF,QACVG,qBACEpR,QAGRqR,MAAA,6BACMpO,QAAQtE,OAASqB,KAAKkO,cAAcvP,OAAS,EAClDqP,GAAchO,MACPA,QAGRsR,IAAA,aAAIjM,EAAOqF,EAAOF,WAIhBf,EAAUC,EAHP6H,EAAkBrN,GAAiBlE,KAAKkO,cAAchB,MAAM,IAAI,GAAO,GAC1EsE,GAAcnM,GAASrF,MAAMoI,SAC7BvL,EAAI,EAEEA,EAAI0U,EAAgB5S,OAAQ9B,IAClC4M,EAAW8H,EAAgB1U,GAC3B2N,IAAWf,EAASlK,OAASoB,gBAAgB8I,EAASpP,SAAS,GAAO,GAAO,KAC7EqP,EAAS8H,EAAW/H,EAASvB,MACnBnB,GAAK0C,EAAUC,EAAQgB,GAAO,EAAM,EAAGjB,GACjDA,EAASlK,OAASoB,gBAAgB8I,EAASpP,SAAS,GAAO,GAAO,UAE5D2F,QAGRwQ,YAAA,qBAAYnW,EAASoX,OAChBC,EAAK1R,KAAKuI,gBAAgBlO,IAAYuM,SAClC6K,KAAYC,EAAKA,EAAKA,EAAG7N,OAAS+C,GAAW6K,MAGtD9J,IAAA,aAAItC,WAIFsM,EAAOD,EAAIE,EAHR/U,EAAIwI,EAAMpC,QAAQtE,OACrBsP,EAASjO,KAAKoI,SACdE,EAAMtI,KAAKsI,IAELzL,MAEN+U,EAAM3D,GADNyD,EAAKrM,EAAM6I,cAAcrR,IACTqL,OACJwJ,EAAGrX,UAAYuX,EAAIvX,SAAYiO,EAAIoJ,EAAGxJ,KAAOI,EAAIoJ,EAAGxJ,IAAI7N,UAAYqX,EAAGrX,UAClFsX,EAAQ3R,KAAKkO,cAAc5I,QAAQoM,EAAGrX,UAAYuX,EAAIvX,QAAUuX,EAAMtJ,EAAIoJ,EAAGxJ,UACxEjF,QAAQgJ,OAAO0F,EAAO,EAAGtM,EAAMpC,QAAQpG,SACvCqR,cAAcjC,OAAO0F,EAAO,EAAGD,UAE/BzO,QAAQrE,KAAKyG,EAAMpC,QAAQpG,SAC3BqR,cAActP,KAAK8S,WAG1BrM,EAAMsG,cAAgB3L,KAAK2L,aAAc,GACzCtG,EAAMwB,SAAW7G,KAAK6G,QAAS,GAC/BmH,GAAchO,MACPA,QAGR6R,QAAA,iBAAQxM,GAUE,SAARyM,GAASC,EAAIC,EAAI5O,UAAQ2O,EAAGzL,YAAc0L,EAAG1L,UAAayL,EAAGzL,UAAY2L,EAAQC,EAASH,EAAGzL,UAAY6L,EAAUC,GAAWxT,KAAKwE,IAAOH,EAAQrE,KAAKwE,GACjI,SAAtBiP,GAAuBN,EAAIC,EAAI5O,UAAOH,EAAQqC,QAAQlC,GAAM,GAAK0O,GAAMC,EAAIC,EAAI5O,OAC/E2O,EAAIC,EAAIzO,EAAGH,EAAIkP,EAAOC,EAAOzN,EAAI1E,EAX9BoS,EAAKnN,EAAM+C,SACdqK,EAAKzS,KAAKoI,SACVgK,EAAY,GACZD,EAAU,GACVF,EAAQ,GACRC,EAAQ,GACRjP,EAAU,GACVyP,EAAKrN,EAAMiD,IACXpI,EAAKF,KAAKsI,QAIN/E,KAAKiP,EACTF,EAAQI,EAAGnP,GACXgP,EAAQrS,EAAGqD,GAEXH,GADA2O,EAAMO,EAAgBvK,GAAoB1C,EAAOrF,KAAMuD,GAAzCiP,EAAGjP,IACTlJ,QACR2X,EAAKS,EAAGlP,GACJgP,GACHnS,EAAK4R,EAAG1L,YAAeiM,EAAMjM,WAAalD,IAAO4O,EAAG3X,QAAW2X,EAAKO,GACpEzN,GAAKwN,GAAUP,EAAGzL,WAAcgM,EAAMhM,WAAalG,EAAG/F,UAAYiY,EAAMjY,QAAkB0X,EAARO,GAE3EhM,WAAalG,EAAGkG,WAAaxB,EAAGzK,UAAY+F,EAAG/F,UACpDyK,EAAGiH,YAAY3L,GAAM+R,EAAUC,GAAWxT,KAAKkG,EAAGzK,QAAS+F,EAAG/F,SAC/D4I,EAAQrE,KAAKkG,EAAGzK,QAAS+F,EAAG/F,UAE5ByX,GAAMhN,EAAI1E,EAAI0E,EAAGzK,SAElBiY,GAASxN,EAAGzK,UAAYiY,EAAMjY,UAAYiY,EAAQE,EAAGjP,IACrD8O,GAAoBvN,EAAGzK,UAAY2X,EAAG3X,SAAWiY,EAAQA,EAAQxN,EAAIkN,EAAIA,EAAG3X,SAC5EgY,GAAoBC,GAASA,EAAMjY,UAAYkY,EAAMlY,QAAUiY,EAAQxN,EAAIyN,EAAOA,EAAMlY,SACxFiY,GAASD,GAAoBC,EAAOC,EAAMlY,UAAYiY,EAAMjY,QAAUkY,EAAQP,EAAIM,EAAMjY,WAEvF2X,EAAuBA,EAAGjG,YAAYgG,GAA2BD,GAAMC,EAAIC,EAAI5O,GAAnCgP,EAAUxT,KAAKwE,GAAtD6O,EAAMrT,KAAKwE,GACjBkP,GAASD,GAAoBC,EAAON,EAAIM,EAAMjY,cAG3CkJ,KAAKkP,EACJD,EAAGjP,KACP2O,EAAMtT,KAAK6T,EAAGlP,GAAGlJ,SACjB6F,EAAGqD,IAAM2O,EAAMtT,KAAKsB,EAAGqD,GAAGlJ,gBAGrB,CAAC8X,QAAAA,EAASC,UAAAA,EAAWH,MAAAA,EAAOC,MAAAA,MAGpCd,mBAAA,sCACKvN,EAAQE,GAAqB/D,KAAK6D,QAAUI,GAC/CpH,EAAImD,KAAKkO,cAAcvP,OACjB9B,KACNkI,GAAoB/E,KAAKkO,cAAcrR,GAAIgH,MAI7CsN,UAAA,mBAAUF,cACL0B,EAAY,QACX1P,QAAQhB,QAAQ,SAAA2C,OAChB8C,EAAK9C,EAAEkH,MACV8G,EAAkB7E,GAAUrG,EAAIuJ,EAAO,EAAI,GAC5CA,GAAQ2B,GAAmBD,EAAUrN,QAAQoC,GAAM,GAAKA,EAAGC,IAAI,kBAAMkL,EAAKC,qBAC1EF,GAAmBD,EAAU/T,KAAK8I,MAElCuJ,GAAQ0B,EAAUhU,QAAUqB,KAAK8S,wBAC7BnH,cAAgB3L,KAAK2L,cAAgBgH,EAAUhU,WAGrDmU,iBAAA,iCACM5E,cAAcjM,QAAQ,SAAAyP,OACtBxT,EAAIwT,EAAGrX,QAAQmH,wBACnBkQ,EAAGpL,aAAepI,EAAE6H,OAAS7H,EAAE8H,QAAU9H,EAAE0B,KAAO1B,EAAE2B,MACpD6R,EAAGzQ,QAAU,OAIfsH,gBAAA,yBAAgBlO,UACR2F,KAAKkO,cAAclO,KAAKiD,QAAQqC,QAAQ/C,EAAOlI,QAGvD0Y,aAAA,+BACQ7O,GAAiBlE,KAAKkO,cAAchB,MAAM,IAAI,GAAM,GAAM3F,IAAI5B,mCAxJ1D1C,EAASf,EAAM8Q,WACrBnP,MAAQ3B,GAAQA,EAAK2B,WACrBgD,UAAY3E,IAAQA,EAAK2E,QAC1BmM,OACE/P,QAAUoE,GAA2BpE,QACrCiL,cAAgBjL,EACrB+K,GAAchO,UACR,MACDiD,QAAUR,EAASQ,OACpBgO,EAAO/O,KAAuB,IAAdA,EAAKwM,MAAmBxM,EAAKJ,QAAUI,EAAKwM,MAChExI,KAAW+K,GAAQ/K,GAAO+M,MAAMrU,KAAKoB,WAChCgR,OAAOC,KAAU/K,WAoJnBS,+BAOLoF,YAAA,qBAAY1G,OACP9D,EAAKvB,KAAK8G,OACb3G,EAAKkF,EAAMyB,cACLvF,EAAG3B,MAAQO,EAAGP,KAAO2B,EAAG1B,OAASM,EAAGN,MAAQ0B,EAAGwE,QAAU5F,EAAG4F,OAASxE,EAAGyE,SAAW7F,EAAG6F,SAAWhG,KAAKT,OAAOgB,OAAO8E,EAAM9F,SAAWS,KAAKgM,UAAY3G,EAAM2G,SAAYhM,KAAK6D,OAASwB,EAAMxB,OAASqP,KAAKC,UAAUnT,KAAK6D,SAAWqP,KAAKC,UAAU9N,EAAMxB,UAGjQmN,OAAA,gBAAOnN,EAAOgD,OACTuM,EAAOpT,KACV3F,EAAU+Y,EAAK/Y,QACf4L,EAAUiF,EAAKsF,YAAYnW,GAC3B0G,EAAQmK,EAAK4E,KAAKC,SAAS1V,GAC3ByM,EAASzM,EAAQmH,wBACjBiO,EAAOpV,EAAQ2E,SAAuC,mBAArB3E,EAAQ2E,SAA8D,QAAnC3E,EAAQgZ,SAAS1W,eAA2BtC,EAAQ2E,UACxHrB,EAAIkJ,EAAS,IAAI9L,EAAS,EAAG,EAAG,EAAG,EAAG+L,EAAOjH,KAAOxD,IAAqByK,EAAOlH,IAAM1D,KAAsByE,gBAAgBtG,GAAS,GAAO,GAAO,GACpJ0G,EAAME,QAAU,EAChBmS,EAAKnN,QAAUA,EACfmN,EAAK/Y,QAAUA,EACf+Y,EAAKlL,GAvhBG,SAAToL,OAASlQ,OACJ8E,EAAK9E,EAAGpG,aAAa,uBACzBkL,GAAM9E,EAAGtH,aAAa,eAAiBoM,EAAK,QAAUmG,KAC/CnG,EAohBGoL,CAAOjZ,GACjB+Y,EAAK7T,OAAS5B,EACdyV,EAAKrS,MAAQA,EACbqS,EAAKtM,OAASA,EACdsM,EAAK9M,aAAeQ,EAAOf,OAASe,EAAOd,QAAUc,EAAOjH,MAAQiH,EAAOlH,KAC3EwT,EAAK7M,QAAUN,EAAQ,WACvBmN,EAAKzT,SAAWsG,EAAQ,YACxBmN,EAAK5U,OAASnE,EAAQM,WACtByY,EAAKlW,EAAI+I,EAAQ,KACjBmN,EAAKjW,EAAI8I,EAAQ,KACjBmN,EAAKjS,OAASJ,EAAMI,OACpBiS,EAAKhS,OAASL,EAAMK,OACpBgS,EAAKzG,SAAW1G,EAAQ,YACxBmN,EAAKxG,MAAQ3G,EAAQ,SACrBmN,EAAKpH,QAAU/F,EAAQ,WACvBmN,EAAKrN,MAAS0J,EAAOA,EAAK1J,MAAQiB,EAAcf,EAAQ,QAAS,MAAO,KACxEmN,EAAKpN,OAASyJ,EAAOA,EAAKzJ,OAASgB,EAAcf,EAAQ,SAAU,MAAO,KAC1EpC,GAzjBc,SAAf0P,aAAgBvO,EAASnB,WACpBoC,EAAUiF,EAAKsF,YAAYxL,EAAQ3K,QAAS,KAAM,UACrDoJ,EAAMuB,EAAQnB,MAAQ,GACtBhH,EAAIgH,EAAMlF,OACJ9B,KACN4G,EAAII,EAAMhH,KAAOoJ,EAAQpC,EAAMhH,IAAM,IAAI2W,OAE1C/P,EAAIgH,SAAWhH,EAAIgH,OAASiG,WAAWjN,EAAIgH,SAAW,GAkjB7C8I,CAAaH,EAAMtP,GAAeD,IAAUD,GAAcC,IACnEuP,EAAKhD,IAAM/V,EAAQuD,QAA6C,QAAnCvD,EAAQgZ,SAAS1W,eAA2Be,EAAQrD,GAAS0F,UAC1FqT,EAAKvM,OAASA,GAA2B,IAAhBjE,EAAOjF,EAAEkB,KAAa+D,EAAOjF,EAAEO,KAAO0E,EAAOjF,EAAEyB,IAAsB,IAAhBwD,EAAOjF,EAAEoB,GACvFqU,EAAKnS,QAAU,uCA1CJ5G,EAASwJ,EAAOgD,QACtBxM,QAAUA,OACV2W,OAAOnN,EAAOgD,SA6Cf4M,4BAQLC,aAAA,sBAAaxL,WACRrL,EAAImD,KAAK2T,OAAOhV,OACb9B,QACFmD,KAAK2T,OAAO9W,GAAGuL,SAASF,UACpBlI,KAAK2T,OAAO9W,MAKtB6R,KAAA,qBACM5M,MAAM2K,OAAOzM,uCAjBPkC,EAAMJ,QACZI,KAAOA,OACPJ,MAAQA,OACR6R,OAAS,QACTxI,SAAWrJ,EAAMqJ,eAiBlByI,2BAaLjM,IAAA,aAAIkM,OACClQ,EAAS3D,KAAKgC,QAAQkF,OAAO,SAAA/D,UAAUA,EAAOjB,OAAS2R,WACvDlQ,EAAOhF,OACHgF,EAAO,IAEfA,EAAS,IAAI8P,EAA8B,mBAAZI,EAAyB,CAACC,QAASD,GAAUA,EAAQ7T,WAC/EgC,QAAQpD,KAAK+E,GACXA,MAGR8I,OAAA,gBAAOtJ,OACFtG,EAAImD,KAAKgC,QAAQsD,QAAQnC,UACxB,GAALtG,GAAUmD,KAAKgC,QAAQiK,OAAOpP,EAAG,GAC1BmD,QAGR+T,SAAA,kBAASC,cACJC,EAAY/N,GACfgO,EAAa/F,SACdjI,GAASlG,MACJqF,MAAMgM,aACN4B,MAAMtU,OAAS,OACfqD,QAAQC,QAAQ,SAAAkB,GAChBA,EAAOjB,KAAK6R,WACf5Q,EAAOwQ,OAAOhV,OAAS,GACvBwP,EAAehL,GACRkC,MAAQlC,EAAOjB,KAAK6R,SAAS5Q,IAErC6Q,GAAS7Q,EAAOwQ,OAAO1R,QAAQ,SAAA0G,UAAKwL,EAAK9O,MAAMsC,IAAIgB,OAEpDwF,EAAe+F,EACfhO,GAAS+N,OACJG,gBACEpU,QAGR8T,QAAA,uBAIEpO,EAAa8D,SAHVyK,EAAY/N,GACfwB,EAAK1H,KAAKmL,SACVtO,EAAImD,KAAKgC,QAAQrD,WAElBuH,GAASlG,KACT0H,EAAG2J,aACElL,KAAKxH,OAASqB,KAAKqG,OAAO1H,OAASqB,KAAK4N,KAAKjP,OAAS,OACtDqD,QAAQC,QAAQ,SAAApD,GACpBA,EAAEqD,KAAK4R,SAAWjV,EAAEqD,KAAK4R,QAAQjV,OAGX8J,EAAGhF,EAFrBkG,EAAUhL,EAAEqD,KAAK2H,QACpBC,EAAUjL,EAAEqD,KAAK4H,QACjB7G,EAAUpE,EAAEoE,QACTA,GAAWA,EAAQtE,SAAWkL,GAAWC,KAC5CnB,EAAI,IAAIf,GACR/I,EAAE8U,OAAO1R,QAAQ,SAAAoD,UAASsD,EAAEhB,IAAItC,MAChC1B,EAASgF,EAAEkJ,QAAQwC,GAAKN,SAAS9Q,KAC1BgP,MAAMtT,QAAUkL,GAAWA,EAAQlG,EAAOsO,OACjDtO,EAAOuO,MAAMvT,QAAUmL,GAAWA,EAAQnG,EAAOuO,UAGnD/K,GAAmBnH,KAAKmG,WACnByH,KAAK3L,QAAQ,SAAAnD,UAAKA,MACvB0K,EAAU9B,EAAG4F,WACb5H,EAAc1F,KAAKqG,OAAO6G,MAAM,GAChCxF,EAAGC,IAAI,WACF6B,GAAW9B,EAAGiG,SACjBjI,EAAYzD,QAAQ,SAAAnD,UAAKA,MACzB+C,EAAcyS,EAAM,iBAGtBpO,GAAS+N,EACFpX,UACDmF,QAAQnF,GAAGqF,KAAKqS,MAAQvU,KAAKgC,QAAQnF,GAAG6R,cAE9C7M,EAAc7B,KAAM,WACpB0H,EAAG8M,UACIxU,QAGRyU,UAAA,mBAAUC,GACAA,EAATA,GAAgB,uBAAM,OAClBC,EAAQ,eACP3S,QAAQC,QAAQ,SAAA7C,MAChBA,EAAE8C,KAAKuS,UAAW,KACjB5X,EAAGiC,EAAI,SAAJA,EAAImE,GACVA,IAAY7D,EAAE6D,QAAUA,KACxBpG,EAAI8X,EAAMrP,QAAQxG,MAEjB6V,EAAM1I,OAAOpP,EAAG,GAChB8X,EAAMhW,QAAU+V,MAGlBC,EAAM/V,KAAKE,GACXM,EAAE8C,KAAKuS,UAAU3V,MAGnB6V,EAAMhW,QAAU+V,IACT1U,QAGR4U,SAAA,gCACM5S,QAAQC,QAAQ,SAAA7C,UAAKA,EAAE6D,QAAU7D,EAAE8C,KAAK0S,UAAYxV,EAAE8C,KAAK0S,SAASxV,KAClEY,QAGRoU,cAAA,uBAAcnD,eACR5L,MAAM8L,UAAUF,QAChBgC,MAAMhR,QAAQ,SAAAoD,UAASA,EAAM8L,UAAUF,KACrCjR,QAGR2J,IAAA,aAAIkL,EAAcb,qBACbhU,OAASkG,KACZ2O,GAAgB7U,KAAK+T,SAASC,QACzBS,UAAU,WACTK,EAAKC,UACTD,EAAKF,WACLE,EAAKhB,cAID9T,QAGRqR,MAAA,eAAM2D,QACA3P,MAAMgM,QACX2D,IAAchV,KAAKgC,QAAQrD,OAAS,MAGrC+U,aAAA,sBAAaxL,WAEXS,EADG9L,EAAImD,KAAKgC,QAAQrD,OAEd9B,QACN8L,EAAI3I,KAAKgC,QAAQnF,GAAG6W,aAAaxL,UAEzBS,SAGF3I,KAAKqF,MAAM+C,SAASF,IAAOlI,KAAKqF,SAGxCqJ,KAAA,qBACMqG,QAAU,OACV1D,eACE/C,EAAatO,KAAKkI,mCAzJdA,QACNA,GAAKA,OACLlG,QAAU,QACViR,MAAQ,QACR5M,OAAS,QACTF,KAAO,QACPyH,KAAO,QACPxC,KAAO,QACP/F,MAAQ,IAAIuC,QACZuD,SAAWD,EAAKC,eAqJVkJ,SAELN,SAAP,kBAAgB9Q,EAASf,OACpBmD,EAAQwC,GAAY5E,EAASf,UACjCiM,GAAgBA,EAAawF,OAAO/U,KAAKyG,GACzCnD,GAAQA,EAAKJ,OAASuS,KAAKvS,MAAMI,EAAKJ,OAAOuD,MAAMsC,IAAItC,GAChDA,QAGD4P,KAAP,cAAY5P,EAAOnD,wBAClBA,EAAOA,GAAQ,MACYA,EAAK0H,YAAa,GACtCN,GAAQjE,EAAOwC,GAAY3F,EAAKe,SAAWoC,EAAMpC,QAAS,CAACY,MAAO3B,EAAK2B,OAASwB,EAAMxB,MAAOgD,OAAQ3E,EAAK2E,OAAQ6H,OAAQxM,EAAKwM,OAAQxM,GAAO,SAG/IgT,GAAP,YAAU7P,EAAOnD,UACToH,GAAQjE,EAAOwC,GAAY3F,EAAKe,SAAWoC,EAAMpC,QAAS,CAACY,MAAO3B,EAAK2B,OAASwB,EAAMxB,MAAOgD,OAAQ3E,EAAK2E,OAAQ6H,OAAQxM,EAAKwM,OAAQxM,EAAM,SAG9IiT,OAAP,gBAAclN,EAAWD,EAAS9F,UAC1BoH,GAAQrB,EAAWD,EAAS9F,SAG7BoP,IAAP,aAAW8D,EAAQC,EAAMnT,OACpB+C,EAAI/C,EAAOsB,EAAMtB,EAAMmL,IAAgB,KAC4BnL,GAAQ+C,EAA7E8E,IAAAA,SAAUW,IAAAA,MAAOsE,IAAAA,QAASnL,IAAAA,MAAOyR,IAAAA,aAAc3G,IAAAA,WAAY9H,IAAAA,OAC5DkI,EAAW7M,GAAQA,EAAK6M,UAAYxM,EAAOL,EAAK6M,UAChDwG,EAAStG,EAAmBoG,EAAMxR,EAAOgD,EAAQuO,GACjDI,EAAQvG,EAAmBmG,EAAQ,EAAGvO,EAAQ0O,GAC9CE,EAAc5R,EAAQE,GAAqBF,GAASI,GACpDyR,EAAMxK,EAAKyK,iBACZ9R,GAASuD,GAAYnC,EAAGsQ,EAAO1R,OAC/BkB,GAAoByQ,EAAOC,GACvBH,wBACmBrQ,IAAOA,EAAE6H,iBAAkB,GACjD7H,EAAE0J,WAAa,WACdvJ,GAAmBoQ,GACnB7G,GAAcA,EAAWnO,MAAMR,KAAM4V,aAGvC7L,GAAYpE,GAAc6P,EAAOD,GACjCtQ,EAAI8B,GAAKyO,EAAOD,EAAQ7K,GAASqE,GAAW9J,EAAEqI,UAAYzJ,EAAOkL,EAAU9J,EAAEqI,UAAY0B,EAAU/J,EAAI,GACtF,iBAAV/C,GAAsB,WAAYA,IAAS+C,EAAEwF,OAASvI,EAAKuI,QAClEiL,IAAQ1G,GAAW0G,EAAI/N,IAAI,kBAAM,kBAAMvC,GAAmBoQ,MACnDxG,EAAU/J,EAAIA,EAAEqI,SAAWpC,EAAKgK,GAAGM,EAAMnb,QAAS4K,GAAK,WAGxD8N,aAAP,sBAAoB8C,EAAiB3T,UAC5B2T,aAA2BjO,GAAYiO,EAAkB,IAAIjO,GAAUiO,EAAiB3T,IAAO6Q,qBAGjGjR,MAAP,eAAaoG,UAELoG,EADApG,EAAPA,GAAY,aACgBoG,EAAapG,GAAM,IAAI0L,EAAU1L,UAGvD4N,YAAP,qBAAmB7S,EAAS8S,IAC1B9S,aAAmB2E,GAAY3E,EAAQA,QAAUR,EAASQ,IAAUhB,QAAQ,SAAA2C,UAAKA,GAAKmJ,GAAUnJ,EAAEkH,OAAoB,IAAbiK,EAAqB,EAAI,WAG7HC,WAAP,oBAAkBxT,OACb1D,EAAIuV,KAAK4B,YAAYzT,WAChB1D,GAAKA,EAAEoX,iBAGVD,YAAP,qBAAmBzT,UACVD,EAAOC,IAAWoE,GAAWkF,YAG/BvD,gBAAP,yBAAuB/F,EAAQqB,UACvB,IAAI8C,GAAapE,EAAOC,GAASqB,SAGlCsS,mBAAP,4BAA0BC,EAAaC,EAAW5V,OAC7C9C,EAAIgD,gBAAgB0V,GAAW,GAAM,GAAMhX,SAASsB,gBAAgByV,WACjE3V,EAAQ9C,EAAE6C,MAAMC,GAAS9C,QAI1B2Y,SAAP,kBAAgBxG,MACf3U,EAA6B,oBAAdob,UAA6BA,SAASnb,KAC1C,CACV8P,EAAO4E,EACP1V,EAAQe,GACRsH,EAAWyI,EAAK6B,MAAMyJ,QACtBpI,EAAiBlD,EAAK4E,KAAK2G,kBACvBC,EAAOxL,EAAK6B,MAAM2J,KAAK,IAC3B1P,EAAgB,uBAACnE,EAAO8E,UAAQ+O,EAAKhG,WAAW7N,GAAS8E,6BAK5D0M,GAAKsC,QAAU,SAaI,oBAAZ9b,QAA2BA,OAAOqQ,MAAQrQ,OAAOqQ,KAAK0L,eAAevC"}