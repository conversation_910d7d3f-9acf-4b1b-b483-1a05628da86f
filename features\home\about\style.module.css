.description {
  margin: 200px;

  display: flex;
  justify-content: center;
  // align-items: center;
  // height: 100vh;
  .body {
    max-width: 1400px;
    display: flex;
    gap: 50px;
    position: relative;
    p {
      margin: 0px;
      &:nth-of-type(1) {
        font-size: 36px;
        gap: 8px;
        line-height: 1.3;
        span {
          margin-right: 3px;
        }
        .mask {
          position: relative;
          overflow: hidden;
          display: inline-flex;
        }
      }
      &:nth-of-type(2) {
        font-size: 18px;
        width: 80%;
        font-weight: 300;
      }
    }
  }
}
