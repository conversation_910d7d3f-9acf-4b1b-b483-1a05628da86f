import { motion, useInView } from "motion/react";
import { useRef } from "react";
import { slideUp } from "./anim";

export default function HomeAbout() {
  const phrase =
    "Rwanda is a beautiful, landlocked country in East-Central Africa bordered by Uganda, Tanzania, Burundi, and the Democratic Republic of Congo. Its capital city is Kigali. The official languages are Kinyarwanda, English, and French, with Swahili also widely spoken. The current president is <PERSON>, leading the country with a vision of unity and progress.";

  const description = useRef(null);
  const isInView = useInView(description);
  return (
    <div ref={description} className=" flex justify-center m-4 my-10 lg:m-24">
      <div className="max-w-[1400px] flex gap-[50px] relative">
        <p className="text-2xl lg:text-[36px] gap-2 leading-10 lg:leading-[1.3]">
          {phrase.split(" ").map((word, index) => {
            return (
              <span
                key={index}
                className="relative overflow-hidden inline-flex mr-[3px]"
              >
                <motion.span
                  variants={slideUp}
                  custom={index}
                  animate={isInView ? "open" : "closed"}
                  key={index}
                >
                  {word}
                </motion.span>
              </span>
            );
          })}
        </p>

        {/* <motion.p
          variants={opacity}
          animate={isInView ? "open" : "closed"}
          className="m-0 text-lg w-4/5 font-light"
        >
          Rwanda is a beautiful, landlocked country in East-Central Africa,
          bordered by Uganda, Tanzania, Burundi, and the Democratic Republic of
          Congo.
        </motion.p> */}
      </div>
    </div>
  );
}
