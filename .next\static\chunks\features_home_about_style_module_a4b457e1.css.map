{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/features/home/<USER>/style.module.css"], "sourcesContent": [".description {\r\n  margin: 200px;\r\n\r\n  display: flex;\r\n  justify-content: center;\r\n  /* // align-items: center;\r\n  // height: 100vh; */\r\n}\r\n\r\n.description .body {\r\n  max-width: 1400px;\r\n  display: flex;\r\n  gap: 50px;\r\n  position: relative;\r\n}\r\n\r\n.description .body p {\r\n  margin: 0px;\r\n}\r\n\r\n.description .body p:nth-of-type(1) {\r\n  font-size: 36px;\r\n  gap: 8px;\r\n  line-height: 1.3;\r\n}\r\n\r\n.description .body p:nth-of-type(1) span {\r\n  margin-right: 3px;\r\n}\r\n\r\n.description .body p:nth-of-type(1) .mask {\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: inline-flex;\r\n}\r\n\r\n.description .body p:nth-of-type(2) {\r\n  font-size: 18px;\r\n  width: 80%;\r\n  font-weight: 300;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;AASA;;;;;;;AAOA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;;;AAMA", "debugId": null}}]}