var R=require("./build/chunks/[turbopack]_runtime.js")("webpack-loaders.js")
R.c("build/chunks/[turbopack-node]_transforms_webpack-loaders_ts_5a40237e._.js")
R.c("build/chunks/[root-of-the-server]__c7ae8543._.js")
R.m("[turbopack-node]/globals.ts [webpack_loaders] (ecmascript)")
R.m("[turbopack-node]/ipc/evaluate.ts/evaluate.js { INNER => \"[turbopack-node]/transforms/webpack-loaders.ts [webpack_loaders] (ecmascript)\", RUNTIME => \"[turbopack-node]/ipc/evaluate.ts [webpack_loaders] (ecmascript)\" } [webpack_loaders] (ecmascript)")
module.exports=R.m("[turbopack-node]/ipc/evaluate.ts/evaluate.js { INNER => \"[turbopack-node]/transforms/webpack-loaders.ts [webpack_loaders] (ecmascript)\", RUNTIME => \"[turbopack-node]/ipc/evaluate.ts [webpack_loaders] (ecmascript)\" } [webpack_loaders] (ecmascript)").exports
