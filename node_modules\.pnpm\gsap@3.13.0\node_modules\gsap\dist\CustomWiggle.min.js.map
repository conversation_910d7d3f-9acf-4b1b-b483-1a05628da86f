{"version": 3, "file": "CustomWiggle.min.js", "sources": ["../src/CustomWiggle.js"], "sourcesContent": ["/*!\n * CustomWiggle 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet gsap, _coreInitted, createCustomEase,\n\t_getGSAP = () => gsap || (typeof(window) !== \"undefined\" && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_eases = {\n\t\teaseOut: \"M0,1,C0.7,1,0.6,0,1,0\",\n\t\teaseInOut: \"M0,0,C0.1,0,0.24,1,0.444,1,0.644,1,0.6,0,1,0\",\n\t\tanticipate: \"M0,0,C0,0.222,0.024,0.386,0,0.4,0.18,0.455,0.65,0.646,0.7,0.67,0.9,0.76,1,0.846,1,1\",\n\t\tuniform: \"M0,0,C0,0.95,0,1,0,1,0,1,1,1,1,1,1,1,1,0,1,0\"\n\t},\n\t_linearEase = p => p,\n\t_initCore = required => {\n\t\tif (!_coreInitted) {\n\t\t\tgsap = _getGSAP();\n\t\t\tcreateCustomEase = gsap && gsap.parseEase(\"_CE\");\n\t\t\tif (createCustomEase) {\n\t\t\t\tfor (let p in _eases) {\n\t\t\t\t\t_eases[p] = createCustomEase(\"\", _eases[p]);\n\t\t\t\t}\n\t\t\t\t_coreInitted = 1;\n\t\t\t\t_create(\"wiggle\").config = vars => typeof(vars) === \"object\" ? _create(\"\", vars) : _create(\"wiggle(\" + vars + \")\", {wiggles:+vars});\n\t\t\t} else {\n\t\t\t\trequired && console.warn(\"Please gsap.registerPlugin(CustomEase, CustomWiggle)\");\n\t\t\t}\n\t\t}\n\t},\n\t_parseEase = (ease, invertNonCustomEases) => {\n\t\tif (typeof(ease) !== \"function\") {\n\t\t\tease = gsap.parseEase(ease) || createCustomEase(\"\", ease);\n\t\t}\n\t\treturn (ease.custom || !invertNonCustomEases) ? ease : p => 1 - ease(p);\n\t},\n\t_bonusValidated = 1, //<name>CustomWiggle</name>\n\t_create = (id, vars) => {\n\t\tif (!_coreInitted) {\n\t\t\t_initCore(1);\n\t\t}\n\t\tvars = vars || {};\n\t\tlet wiggles = (vars.wiggles || 10) | 0,\n\t\t\tinc = 1 / wiggles,\n\t\t\tx = inc / 2,\n\t\t\tanticipate = (vars.type === \"anticipate\"),\n\t\t\tyEase = _eases[vars.type] || _eases.easeOut,\n\t\t\txEase = _linearEase,\n\t\t\trnd = 1000,\n\t\t\tnextX, nextY, angle, handleX, handleY, easedX, y, path, i;\n\t\tif (_bonusValidated) {\n\t\t\tif (anticipate) { //the anticipate ease is actually applied on the x-axis (timing) and uses easeOut for amplitude.\n\t\t\t\txEase = yEase;\n\t\t\t\tyEase = _eases.easeOut;\n\t\t\t}\n\t\t\tif (vars.timingEase) {\n\t\t\t\txEase = _parseEase(vars.timingEase);\n\t\t\t}\n\t\t\tif (vars.amplitudeEase) {\n\t\t\t\tyEase = _parseEase(vars.amplitudeEase, true);\n\t\t\t}\n\t\t\teasedX = xEase(x);\n\t\t\ty = anticipate ? -yEase(x) : yEase(x);\n\t\t\tpath = [0, 0, easedX / 4, 0, easedX / 2, y, easedX, y];\n\n\t\t\tif (vars.type === \"random\") { //if we just select random values on the y-axis and plug them into the \"normal\" algorithm, since the control points are always straight horizontal, it creates a bit of a slowdown at each anchor which just didn't seem as desirable, so we switched to an algorithm that bends the control points to be more in line with their context.\n\t\t\t\tpath.length = 4;\n\t\t\t\tnextX = xEase(inc);\n\t\t\t\tnextY = Math.random() * 2 - 1;\n\t\t\t\tfor (i = 2; i < wiggles; i++) {\n\t\t\t\t\tx = nextX;\n\t\t\t\t\ty = nextY;\n\t\t\t\t\tnextX = xEase(inc * i);\n\t\t\t\t\tnextY = Math.random() * 2 - 1;\n\t\t\t\t\tangle = Math.atan2(nextY - path[path.length - 3], nextX - path[path.length - 4]);\n\t\t\t\t\thandleX = Math.cos(angle) * inc;\n\t\t\t\t\thandleY = Math.sin(angle) * inc;\n\t\t\t\t\tpath.push(x - handleX, y - handleY, x, y, x + handleX, y + handleY);\n\t\t\t\t}\n\t\t\t\tpath.push(nextX, 0, 1, 0);\n\t\t\t} else {\n\t\t\t\tfor (i = 1; i < wiggles; i++) {\n\t\t\t\t\tpath.push(xEase(x + inc / 2), y);\n\t\t\t\t\tx += inc;\n\t\t\t\t\ty = ((y > 0) ? -1 : 1) * (yEase(i * inc));\n\t\t\t\t\teasedX = xEase(x);\n\t\t\t\t\tpath.push(xEase(x - inc / 2), y, easedX, y);\n\t\t\t\t}\n\t\t\t\tpath.push(xEase(x + inc / 4), y, xEase(x + inc / 4), 0, 1, 0);\n\t\t\t}\n\t\t\ti = path.length;\n\t\t\twhile (--i > -1) {\n\t\t\t\tpath[i] = ~~(path[i] * rnd) / rnd; //round values to avoid odd strings for super tiny values\n\t\t\t}\n\t\t\tpath[2] = \"C\" + path[2];\n\t\t\treturn createCustomEase(id, \"M\" + path.join(\",\"));\n\t\t}\n\t};\n\nexport class CustomWiggle {\n\n\tconstructor(id, vars) {\n\t\tthis.ease = _create(id, vars);\n\t}\n\n\tstatic create(id, vars) {\n\t\treturn _create(id, vars);\n\t}\n\n\tstatic register(core) {\n\t\tgsap = core;\n\t\t_initCore();\n\t}\n\n}\n\n_getGSAP() && gsap.registerPlugin(CustomWiggle);\n\nCustomWiggle.version = \"3.13.0\";\n\nexport { CustomWiggle as default };"], "names": ["_getGSAP", "gsap", "window", "registerPlugin", "_linearEase", "p", "_initCore", "required", "_coreInitted", "createCustomEase", "parseEase", "_eases", "_create", "config", "vars", "wiggles", "console", "warn", "_parseEase", "ease", "invertNonCustomEases", "custom", "easeOut", "easeInOut", "anticipate", "uniform", "id", "nextX", "nextY", "angle", "handleX", "handleY", "easedX", "y", "path", "i", "inc", "x", "type", "yEase", "xEase", "timingEase", "amplitudeEase", "length", "Math", "random", "atan2", "cos", "sin", "push", "join", "CustomWiggle", "create", "register", "core", "version"], "mappings": ";;;;;;;;;6MAWY,SAAXA,WAAiBC,GAA4B,oBAAZC,SAA4BD,EAAOC,OAAOD,OAASA,EAAKE,gBAAkBF,EAO7F,SAAdG,EAAcC,UAAKA,EACP,SAAZC,EAAYC,OACNC,KACJP,EAAOD,IACPS,EAAmBR,GAAQA,EAAKS,UAAU,OACpB,KAChB,IAAIL,KAAKM,EACbA,EAAON,GAAKI,EAAiB,GAAIE,EAAON,IAEzCG,EAAe,EACfI,EAAQ,UAAUC,OAAS,SAAAC,SAAyB,iBAAVA,EAAqBF,EAAQ,GAAIE,GAAQF,EAAQ,UAAYE,EAAO,IAAK,CAACC,SAASD,UAE7HP,GAAYS,QAAQC,KAAK,wDAIf,SAAbC,EAAcC,EAAMC,SACE,mBAAVD,IACVA,EAAOlB,EAAKS,UAAUS,IAASV,EAAiB,GAAIU,IAE7CA,EAAKE,SAAWD,EAAwBD,EAAO,SAAAd,UAAK,EAAIc,EAAKd,QA5BnEJ,EAAMO,EAAcC,EAEvBE,EAAS,CACRW,QAAS,wBACTC,UAAW,+CACXC,WAAY,sFACZC,QAAS,gDAyBVb,EAAU,SAAVA,QAAWc,EAAIZ,GACTN,GACJF,EAAU,OAUVqB,EAAOC,EAAOC,EAAOC,EAASC,EAASC,EAAQC,EAAGC,EAAMC,EAPrDpB,EAAiC,IADrCD,EAAOA,GAAQ,IACKC,SAAW,IAC9BqB,EAAM,EAAIrB,EACVsB,EAAID,EAAM,EACVZ,EAA4B,eAAdV,EAAKwB,KACnBC,EAAQ5B,EAAOG,EAAKwB,OAAS3B,EAAOW,QACpCkB,EAAQpC,KAIJoB,IACHgB,EAAQD,EACRA,EAAQ5B,EAAOW,SAEZR,EAAK2B,aACRD,EAAQtB,EAAWJ,EAAK2B,aAErB3B,EAAK4B,gBACRH,EAAQrB,EAAWJ,EAAK4B,eAAe,IAIxCR,EAAO,CAAC,EAAG,GAFXF,EAASQ,EAAMH,IAEQ,EAAG,EAAGL,EAAS,EADtCC,EAAIT,GAAce,EAAMF,GAAKE,EAAMF,GACSL,EAAQC,GAElC,WAAdnB,EAAKwB,KAAmB,KAC3BJ,EAAKS,OAAS,EACdhB,EAAQa,EAAMJ,GACdR,EAAwB,EAAhBgB,KAAKC,SAAe,EACvBV,EAAI,EAAGA,EAAIpB,EAASoB,IACxBE,EAAIV,EACJM,EAAIL,EACJD,EAAQa,EAAMJ,EAAMD,GACpBP,EAAwB,EAAhBgB,KAAKC,SAAe,EAC5BhB,EAAQe,KAAKE,MAAMlB,EAAQM,EAAKA,EAAKS,OAAS,GAAIhB,EAAQO,EAAKA,EAAKS,OAAS,IAC7Eb,EAAUc,KAAKG,IAAIlB,GAASO,EAC5BL,EAAUa,KAAKI,IAAInB,GAASO,EAC5BF,EAAKe,KAAKZ,EAAIP,EAASG,EAAIF,EAASM,EAAGJ,EAAGI,EAAIP,EAASG,EAAIF,GAE5DG,EAAKe,KAAKtB,EAAO,EAAG,EAAG,OACjB,KACDQ,EAAI,EAAGA,EAAIpB,EAASoB,IACxBD,EAAKe,KAAKT,EAAMH,EAAID,EAAM,GAAIH,GAC9BI,GAAKD,EACLH,GAAU,EAAJA,GAAU,EAAI,GAAMM,EAAMJ,EAAIC,GACpCJ,EAASQ,EAAMH,GACfH,EAAKe,KAAKT,EAAMH,EAAID,EAAM,GAAIH,EAAGD,EAAQC,GAE1CC,EAAKe,KAAKT,EAAMH,EAAID,EAAM,GAAIH,EAAGO,EAAMH,EAAID,EAAM,GAAI,EAAG,EAAG,OAE5DD,EAAID,EAAKS,QACK,IAALR,GACRD,EAAKC,MA5CA,IA4CQD,EAAKC,IA5Cb,WA8CND,EAAK,GAAK,IAAMA,EAAK,GACdzB,EAAiBiB,EAAI,IAAMQ,EAAKgB,KAAK,OAIlCC,gBAMLC,OAAP,gBAAc1B,EAAIZ,UACVF,EAAQc,EAAIZ,iBAGbuC,SAAP,kBAAgBC,GACfrD,EAAOqD,EACPhD,yCAVWoB,EAAIZ,QACVK,KAAOP,EAAQc,EAAIZ,GAc1Bd,KAAcC,EAAKE,eAAegD,GAElCA,EAAaI,QAAU"}