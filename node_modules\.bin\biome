#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/@biomejs+biome@2.2.0/node_modules/@biomejs/biome/bin/node_modules:/mnt/c/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/@biomejs+biome@2.2.0/node_modules/@biomejs/biome/node_modules:/mnt/c/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/@biomejs+biome@2.2.0/node_modules/@biomejs/node_modules:/mnt/c/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/@biomejs+biome@2.2.0/node_modules:/mnt/c/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/@biomejs+biome@2.2.0/node_modules/@biomejs/biome/bin/node_modules:/mnt/c/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/@biomejs+biome@2.2.0/node_modules/@biomejs/biome/node_modules:/mnt/c/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/@biomejs+biome@2.2.0/node_modules/@biomejs/node_modules:/mnt/c/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/@biomejs+biome@2.2.0/node_modules:/mnt/c/Users/<USER>/Documents/nProjects/other/connect/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@biomejs/biome/bin/biome" "$@"
else
  exec node  "$basedir/../@biomejs/biome/bin/biome" "$@"
fi
