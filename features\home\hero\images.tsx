import { motion } from "motion/react";
import Image from "next/image";
import { useEffect, useState } from "react";
import cover from "@/public/images/a.jpg";
import cover2 from "@/public/images/d.jpg";

const images = [cover, cover2];
export default function ImagesHero() {
  const [index, setIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setIndex((prev) => (prev + 1) % images.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="absolute inset-0">
      {images.map((img, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, scale: 1 }}
          animate={{
            opacity: i === index ? 1 : 0,
            scale: i === index ? 1.2 : 1, // scale active image
          }}
          transition={{
            duration: 1.5,
            ease: "easeInOut",
          }}
          className="absolute inset-0"
        >
          <Image src={img} alt="bg" fill className="object-cover" priority />
        </motion.div>
      ))}
    </div>
  );
}
