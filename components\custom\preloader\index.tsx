import { motion } from "motion/react";
import { useEffect, useState } from "react";
import { opacity, slideUp } from "./anim";

// import { opacity, slideUp } from "./anim";
// import styles from "./style.module.scss";

const words = [
  "Hello",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>ia<PERSON>",
  "<PERSON><PERSON><PERSON>",
  "やあ",
  "Hallå",
  "Guten tag",
  "Hallo",
  "Amakuru",
];

export default function Preloader() {
  const [index, setIndex] = useState(0);
  const [dimension, setDimension] = useState({ width: 0, height: 0 });

  useEffect(() => {
    setDimension({ width: window.innerWidth, height: window.innerHeight });
  }, []);

  useEffect(() => {
    if (index === words.length - 1) return;
    setTimeout(
      () => {
        setIndex(index + 1);
      },
      index === 0 ? 1000 : 150,
    );
  }, [index]);

  const initialPath = `M0 0 L${dimension.width} 0 L${dimension.width} ${dimension.height} Q${dimension.width / 2} ${dimension.height + 300} 0 ${dimension.height}  L0 0`;
  const targetPath = `M0 0 L${dimension.width} 0 L${dimension.width} ${dimension.height} Q${dimension.width / 2} ${dimension.height} 0 ${dimension.height}  L0 0`;

  const curve = {
    initial: {
      d: initialPath,
      transition: { duration: 0.7, ease: [0.76, 0, 0.24, 1] as const },
    },
    exit: {
      d: targetPath,
      transition: {
        duration: 0.7,
        ease: [0.76, 0, 0.24, 1] as const,
        delay: 0.3,
      },
    },
  };

  return (
    <motion.div
      variants={slideUp}
      initial="initial"
      exit="exit"
      className="h-screen w-screen fixed flex bg-white items-center justify-center  z-99"
    >
      {dimension.width > 0 && (
        <>
          <motion.p
            variants={opacity}
            initial="initial"
            animate="enter"
            className="flex text-black items-center absolute z-10 text-5xl lg:text-7xl 2xl:text-9xl"
          >
            <span className="block size-5 bg-black rounded-full mr-2.5"></span>
            {words[index]}
          </motion.p>
          <svg className="absolute top-0 w-full h-[calc(100%+300px)]">
            <title>Wave</title>
            <motion.path
              variants={curve}
              initial="initial"
              exit="exit"
              className="fill-white"
            ></motion.path>
          </svg>
        </>
      )}
    </motion.div>
  );
}
