/* [project]/features/home/<USER>/style.module.css [app-client] (css) */
.style-module__ou2qJG__description {
  justify-content: center;
  margin: 200px;
  display: flex;
}

.style-module__ou2qJG__description .style-module__ou2qJG__body {
  gap: 50px;
  max-width: 1400px;
  display: flex;
  position: relative;
}

.style-module__ou2qJG__description .style-module__ou2qJG__body p {
  margin: 0;
}

.style-module__ou2qJG__description .style-module__ou2qJG__body p:first-of-type {
  gap: 8px;
  font-size: 36px;
  line-height: 1.3;
}

.style-module__ou2qJG__description .style-module__ou2qJG__body p:first-of-type span {
  margin-right: 3px;
}

.style-module__ou2qJG__description .style-module__ou2qJG__body p:first-of-type .style-module__ou2qJG__mask {
  display: inline-flex;
  position: relative;
  overflow: hidden;
}

.style-module__ou2qJG__description .style-module__ou2qJG__body p:nth-of-type(2) {
  width: 80%;
  font-size: 18px;
  font-weight: 300;
}

/*# sourceMappingURL=features_home_about_style_module_a4b457e1.css.map*/