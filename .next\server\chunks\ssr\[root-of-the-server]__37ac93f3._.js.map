{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/components/custom/preloader/anim.ts"], "sourcesContent": ["export const opacity = {\r\n  initial: {\r\n    opacity: 0,\r\n  },\r\n  enter: {\r\n    opacity: 0.75,\r\n    transition: { duration: 1, delay: 0.2 },\r\n  },\r\n};\r\n\r\nexport const slideUp = {\r\n  initial: {\r\n    top: 0,\r\n  },\r\n  exit: {\r\n    top: \"-100vh\",\r\n    transition: {\r\n      duration: 0.8,\r\n      ease: [0.76, 0, 0.24, 1] as const,\r\n      delay: 0.2,\r\n    },\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAAO,MAAM,UAAU;IACrB,SAAS;QACP,SAAS;IACX;IACA,OAAO;QACL,SAAS;QACT,YAAY;YAAE,UAAU;YAAG,OAAO;QAAI;IACxC;AACF;AAEO,MAAM,UAAU;IACrB,SAAS;QACP,KAAK;IACP;IACA,MAAM;QACJ,KAAK;QACL,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAM;gBAAG;gBAAM;aAAE;YACxB,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/components/custom/preloader/index.tsx"], "sourcesContent": ["import { motion } from \"motion/react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { opacity, slideUp } from \"./anim\";\r\n\r\n// import { opacity, slideUp } from \"./anim\";\r\n// import styles from \"./style.module.scss\";\r\n\r\nconst words = [\r\n  \"Hello\",\r\n  \"<PERSON><PERSON><PERSON>\",\r\n  \"<PERSON>ia<PERSON>\",\r\n  \"<PERSON><PERSON><PERSON>\",\r\n  \"やあ\",\r\n  \"Hallå\",\r\n  \"Guten tag\",\r\n  \"Hallo\",\r\n  \"Amakuru\",\r\n];\r\n\r\nexport default function Preloader() {\r\n  const [index, setIndex] = useState(0);\r\n  const [dimension, setDimension] = useState({ width: 0, height: 0 });\r\n\r\n  useEffect(() => {\r\n    setDimension({ width: window.innerWidth, height: window.innerHeight });\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (index === words.length - 1) return;\r\n    setTimeout(\r\n      () => {\r\n        setIndex(index + 1);\r\n      },\r\n      index === 0 ? 1000 : 150,\r\n    );\r\n  }, [index]);\r\n\r\n  const initialPath = `M0 0 L${dimension.width} 0 L${dimension.width} ${dimension.height} Q${dimension.width / 2} ${dimension.height + 300} 0 ${dimension.height}  L0 0`;\r\n  const targetPath = `M0 0 L${dimension.width} 0 L${dimension.width} ${dimension.height} Q${dimension.width / 2} ${dimension.height} 0 ${dimension.height}  L0 0`;\r\n\r\n  const curve = {\r\n    initial: {\r\n      d: initialPath,\r\n      transition: { duration: 0.7, ease: [0.76, 0, 0.24, 1] as const },\r\n    },\r\n    exit: {\r\n      d: targetPath,\r\n      transition: {\r\n        duration: 0.7,\r\n        ease: [0.76, 0, 0.24, 1] as const,\r\n        delay: 0.3,\r\n      },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      variants={slideUp}\r\n      initial=\"initial\"\r\n      exit=\"exit\"\r\n      className=\"h-screen w-screen fixed flex bg-white items-center justify-center  z-99\"\r\n    >\r\n      {dimension.width > 0 && (\r\n        <>\r\n          <motion.p\r\n            variants={opacity}\r\n            initial=\"initial\"\r\n            animate=\"enter\"\r\n            className=\"flex text-black items-center absolute z-10 text-5xl lg:text-7xl 2xl:text-9xl\"\r\n          >\r\n            <span className=\"block size-5 bg-black rounded-full mr-2.5\"></span>\r\n            {words[index]}\r\n          </motion.p>\r\n          <svg className=\"absolute top-0 w-full h-[calc(100%+300px)]\">\r\n            <title>Wave</title>\r\n            <motion.path\r\n              variants={curve}\r\n              initial=\"initial\"\r\n              exit=\"exit\"\r\n              className=\"fill-white\"\r\n            ></motion.path>\r\n          </svg>\r\n        </>\r\n      )}\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,6CAA6C;AAC7C,4CAA4C;AAE5C,MAAM,QAAQ;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,gVAAQ,EAAC;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,gVAAQ,EAAC;QAAE,OAAO;QAAG,QAAQ;IAAE;IAEjE,IAAA,iVAAS,EAAC;QACR,aAAa;YAAE,OAAO,OAAO,UAAU;YAAE,QAAQ,OAAO,WAAW;QAAC;IACtE,GAAG,EAAE;IAEL,IAAA,iVAAS,EAAC;QACR,IAAI,UAAU,MAAM,MAAM,GAAG,GAAG;QAChC,WACE;YACE,SAAS,QAAQ;QACnB,GACA,UAAU,IAAI,OAAO;IAEzB,GAAG;QAAC;KAAM;IAEV,MAAM,cAAc,CAAC,MAAM,EAAE,UAAU,KAAK,CAAC,IAAI,EAAE,UAAU,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM,CAAC,EAAE,EAAE,UAAU,KAAK,GAAG,EAAE,CAAC,EAAE,UAAU,MAAM,GAAG,IAAI,GAAG,EAAE,UAAU,MAAM,CAAC,MAAM,CAAC;IACtK,MAAM,aAAa,CAAC,MAAM,EAAE,UAAU,KAAK,CAAC,IAAI,EAAE,UAAU,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM,CAAC,EAAE,EAAE,UAAU,KAAK,GAAG,EAAE,CAAC,EAAE,UAAU,MAAM,CAAC,GAAG,EAAE,UAAU,MAAM,CAAC,MAAM,CAAC;IAE/J,MAAM,QAAQ;QACZ,SAAS;YACP,GAAG;YACH,YAAY;gBAAE,UAAU;gBAAK,MAAM;oBAAC;oBAAM;oBAAG;oBAAM;iBAAE;YAAU;QACjE;QACA,MAAM;YACJ,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAM;oBAAG;oBAAM;iBAAE;gBACxB,OAAO;YACT;QACF;IACF;IAEA,qBACE,6WAAC,0SAAM,CAAC,GAAG;QACT,UAAU,oJAAO;QACjB,SAAQ;QACR,MAAK;QACL,WAAU;kBAET,UAAU,KAAK,GAAG,mBACjB;;8BACE,6WAAC,0SAAM,CAAC,CAAC;oBACP,UAAU,oJAAO;oBACjB,SAAQ;oBACR,SAAQ;oBACR,WAAU;;sCAEV,6WAAC;4BAAK,WAAU;;;;;;wBACf,KAAK,CAAC,MAAM;;;;;;;8BAEf,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;sCAAM;;;;;;sCACP,6WAAC,0SAAM,CAAC,IAAI;4BACV,UAAU;4BACV,SAAQ;4BACR,MAAK;4BACL,WAAU;;;;;;;;;;;;;;;;;;;AAOxB", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,oOAAO,EAAC,IAAA,8LAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/components/custom/page-container.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\r\n\r\nfunction PageContainer({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"section\">) {\r\n  return (\r\n    <section {...props}>\r\n      <div className=\"container-wrapper\">\r\n        <div className={cn(\"container\", className)}>{children}</div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\nfunction PageContainerHeading({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"h1\">) {\r\n  return (\r\n    <h1\r\n      className={cn(\r\n        \"max-w-2xl text-balance font-semibold text-4xl text-primary leading-tighter tracking-tight lg:max-w-3xl lg:font-semibold lg:leading-[1.1] xl:text-5xl xl:tracking-tighter\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction PageContainerDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"p\">) {\r\n  return (\r\n    <p\r\n      className={cn(\r\n        \"max-w-3xl text-balance text-base text-foreground sm:text-lg\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction PageContainerActions({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex w-full items-center justify-center gap-2 pt-2 **:data-[slot=button]:shadow-none\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  PageContainerActions,\r\n  PageContainer,\r\n  PageContainerDescription,\r\n  PageContainerHeading,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OAC6B;IAChC,qBACE,6WAAC;QAAS,GAAG,KAAK;kBAChB,cAAA,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAW,IAAA,kHAAE,EAAC,aAAa;0BAAa;;;;;;;;;;;;;;;;AAIrD;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,6WAAC;QACC,WAAW,IAAA,kHAAE,EACX,4KACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,GAAG,OACuB;IAC1B,qBACE,6WAAC;QACC,WAAW,IAAA,kHAAE,EACX,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6WAAC;QACC,WAAW,IAAA,kHAAE,EACX,wFACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/features/home/<USER>"], "sourcesContent": ["import { PageContainer } from \"@/components/custom/page-container\";\r\n\r\nexport default function AboutHome() {\r\n  return <PageContainer className=\"min-h-screen py-6\">AboutHome</PageContainer>;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEe,SAAS;IACtB,qBAAO,6WAAC,2JAAa;QAAC,WAAU;kBAAoB;;;;;;AACtD", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/public/images/a.jpg.mjs%20%28structured%20image%20object%20with%20data%20url%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 4032, height: 3024, blurWidth: 8, blurHeight: 6, blurDataURL: \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAgAAAQABAAD/wAARCAAGAAgDAREAAhEBAxEB/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwCRJtauD5UF/GpIPzPH1I4NV7eT3RzOg1sz/9k=\"};\n"], "names": [], "mappings": ";;;;AAAA;;uCACe;IAAE,KAAA,yHAAG;IAAE,OAAO;IAAM,QAAQ;IAAM,WAAW;IAAG,YAAY;IAAG,aAAa;AAA63B", "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/public/images/d.jpg.mjs%20%28structured%20image%20object%20with%20data%20url%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 6880, height: 5504, blurWidth: 8, blurHeight: 6, blurDataURL: \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAgAAAQABAAD/wAARCAAGAAgDAREAAhEBAxEB/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwClqWi3OosHuLwL8+1Qi54x1Oe9eBRrwgrJdDlVmf/Z\"};\n"], "names": [], "mappings": ";;;;AAAA;;uCACe;IAAE,KAAA,yHAAG;IAAE,OAAO;IAAM,QAAQ;IAAM,WAAW;IAAG,YAAY;IAAG,aAAa;AAAi4B", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/features/home/<USER>/images.tsx"], "sourcesContent": ["import { motion } from \"motion/react\";\r\nimport Image from \"next/image\";\r\nimport { useEffect, useState } from \"react\";\r\nimport cover from \"@/public/images/a.jpg\";\r\nimport cover2 from \"@/public/images/d.jpg\";\r\n\r\nconst images = [cover, cover2];\r\nexport default function ImagesHero() {\r\n  const [index, setIndex] = useState(0);\r\n\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      setIndex((prev) => (prev + 1) % images.length);\r\n    }, 4000);\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"absolute inset-0\">\r\n      {images.map((img, i) => (\r\n        <motion.div\r\n          key={i}\r\n          initial={{ opacity: 0, scale: 1 }}\r\n          animate={{\r\n            opacity: i === index ? 1 : 0,\r\n            scale: i === index ? 1.2 : 1, // scale active image\r\n          }}\r\n          transition={{\r\n            duration: 1.5,\r\n            ease: \"easeInOut\",\r\n          }}\r\n          className=\"absolute inset-0\"\r\n        >\r\n          <Image src={img} alt=\"bg\" fill className=\"object-cover\" priority />\r\n        </motion.div>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,SAAS;IAAC,sSAAK;IAAE,sSAAM;CAAC;AACf,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,gVAAQ,EAAC;IAEnC,IAAA,iVAAS,EAAC;QACR,MAAM,WAAW,YAAY;YAC3B,SAAS,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,OAAO,MAAM;QAC/C,GAAG;QACH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,qBACE,6WAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAC,KAAK,kBAChB,6WAAC,0SAAM,CAAC,GAAG;gBAET,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,SAAS;oBACP,SAAS,MAAM,QAAQ,IAAI;oBAC3B,OAAO,MAAM,QAAQ,MAAM;gBAC7B;gBACA,YAAY;oBACV,UAAU;oBACV,MAAM;gBACR;gBACA,WAAU;0BAEV,cAAA,6WAAC,uQAAK;oBAAC,KAAK;oBAAK,KAAI;oBAAK,IAAI;oBAAC,WAAU;oBAAe,QAAQ;;;;;;eAZ3D;;;;;;;;;;AAiBf", "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/features/home/<USER>/paragraphs.tsx"], "sourcesContent": ["import gsap from \"gsap\";\r\nimport { ScrollTrigger } from \"gsap/all\";\r\nimport { useCallback, useEffect, useRef } from \"react\";\r\n\r\nexport default function ScrollParagraphHero() {\r\n  const firstText = useRef<HTMLParagraphElement>(null);\r\n  const secondText = useRef<HTMLParagraphElement>(null);\r\n  const slider = useRef<HTMLDivElement>(null);\r\n  const xPercent = useRef(0);\r\n  const direction = useRef(-1);\r\n\r\n  const animate = useCallback(() => {\r\n    if (xPercent.current < -100) {\r\n      xPercent.current = 0;\r\n    } else if (xPercent.current > 0) {\r\n      xPercent.current = -100;\r\n    }\r\n    gsap.set(firstText.current, { xPercent: xPercent.current });\r\n    gsap.set(secondText.current, { xPercent: xPercent.current });\r\n    requestAnimationFrame(animate);\r\n    xPercent.current += 0.08 * direction.current;\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    gsap.registerPlugin(ScrollTrigger);\r\n    gsap.to(slider.current, {\r\n      scrollTrigger: {\r\n        trigger: document.documentElement,\r\n        scrub: 0.25,\r\n        start: 0,\r\n        end: window.innerHeight,\r\n        onUpdate: (e) => {\r\n          direction.current = e.direction * -1;\r\n        },\r\n      },\r\n      x: \"-500px\",\r\n    });\r\n    requestAnimationFrame(animate);\r\n  }, [animate]);\r\n\r\n  return (\r\n    <div className=\"absolute top-[calc(100vh-300px)]\">\r\n      <div ref={slider} className=\"relative whitespace-nowrap\">\r\n        <p\r\n          ref={firstText}\r\n          className=\"relative m-0 font-edu text-white text-7xl lg:text-[230px] pr-[50px] font-medium\"\r\n        >\r\n          Welcome in Rwanda -\r\n        </p>\r\n        <p\r\n          ref={secondText}\r\n          className=\"absolute left-full font-edu top-0 m-0 text-7xl text-white lg:text-[230px] pr-[50px] font-medium\"\r\n        >\r\n          Welcome in Rwanda -\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEe,SAAS;IACtB,MAAM,YAAY,IAAA,8UAAM,EAAuB;IAC/C,MAAM,aAAa,IAAA,8UAAM,EAAuB;IAChD,MAAM,SAAS,IAAA,8UAAM,EAAiB;IACtC,MAAM,WAAW,IAAA,8UAAM,EAAC;IACxB,MAAM,YAAY,IAAA,8UAAM,EAAC,CAAC;IAE1B,MAAM,UAAU,IAAA,mVAAW,EAAC;QAC1B,IAAI,SAAS,OAAO,GAAG,CAAC,KAAK;YAC3B,SAAS,OAAO,GAAG;QACrB,OAAO,IAAI,SAAS,OAAO,GAAG,GAAG;YAC/B,SAAS,OAAO,GAAG,CAAC;QACtB;QACA,0MAAI,CAAC,GAAG,CAAC,UAAU,OAAO,EAAE;YAAE,UAAU,SAAS,OAAO;QAAC;QACzD,0MAAI,CAAC,GAAG,CAAC,WAAW,OAAO,EAAE;YAAE,UAAU,SAAS,OAAO;QAAC;QAC1D,sBAAsB;QACtB,SAAS,OAAO,IAAI,OAAO,UAAU,OAAO;IAC9C,GAAG,EAAE;IAEL,IAAA,iVAAS,EAAC;QACR,0MAAI,CAAC,cAAc,CAAC,wMAAa;QACjC,0MAAI,CAAC,EAAE,CAAC,OAAO,OAAO,EAAE;YACtB,eAAe;gBACb,SAAS,SAAS,eAAe;gBACjC,OAAO;gBACP,OAAO;gBACP,KAAK,OAAO,WAAW;gBACvB,UAAU,CAAC;oBACT,UAAU,OAAO,GAAG,EAAE,SAAS,GAAG,CAAC;gBACrC;YACF;YACA,GAAG;QACL;QACA,sBAAsB;IACxB,GAAG;QAAC;KAAQ;IAEZ,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,KAAK;YAAQ,WAAU;;8BAC1B,6WAAC;oBACC,KAAK;oBACL,WAAU;8BACX;;;;;;8BAGD,6WAAC;oBACC,KAAK;oBACL,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/features/home/<USER>/index.tsx"], "sourcesContent": ["\"use client\";\r\nimport { motion, useScroll, useTransform } from \"motion/react\";\r\nimport { useRef } from \"react\";\r\nimport ImagesHero from \"./images\";\r\nimport ScrollParagraphHero from \"./paragraphs\";\r\nexport default function HomeHero() {\r\n  const container = useRef<HTMLDivElement>(null);\r\n  const { scrollYProgress } = useScroll({\r\n    target: container,\r\n    offset: [\"start start\", \"end start\"],\r\n  });\r\n\r\n  const y = useTransform(scrollYProgress, [0, 1], [\"0vh\", \"120vh\"]);\r\n  return (\r\n    <main className=\"bg-white relative flex h-screen overflow-hidden\">\r\n      <motion.div\r\n        style={{ y }}\r\n        className=\"relative h-full w-full\"\r\n        ref={container}\r\n      >\r\n        <div className=\"flex justify-between z-10 opacity-100 px-4 lg:px-6 py-5 \">\r\n          <p className=\"text-2xl text-white font-edu tracking-tighter font-semibold\">\r\n            Rwanda Connect\r\n          </p>\r\n        </div>\r\n        <ImagesHero />\r\n        <ScrollParagraphHero />\r\n      </motion.div>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AAAA;AACA;AACA;AACA;AAJA;;;;;;AAKe,SAAS;IACtB,MAAM,YAAY,IAAA,8UAAM,EAAiB;IACzC,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,4RAAS,EAAC;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAe;SAAY;IACtC;IAEA,MAAM,IAAI,IAAA,kSAAY,EAAC,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAO;KAAQ;IAChE,qBACE,6WAAC;QAAK,WAAU;kBACd,cAAA,6WAAC,0SAAM,CAAC,GAAG;YACT,OAAO;gBAAE;YAAE;YACX,WAAU;YACV,KAAK;;8BAEL,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAE,WAAU;kCAA8D;;;;;;;;;;;8BAI7E,6WAAC,8IAAU;;;;;8BACX,6WAAC,kJAAmB;;;;;;;;;;;;;;;;AAI5B", "debugId": null}}, {"offset": {"line": 585, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/nProjects/other/connect/app/page.tsx"], "sourcesContent": ["\"use client\";\nimport Lenis from \"lenis\";\nimport { AnimatePresence } from \"motion/react\";\nimport { useEffect, useState } from \"react\";\nimport Preloader from \"@/components/custom/preloader\";\nimport AboutHome from \"@/features/home/<USER>\";\nimport HomeHero from \"@/features/home/<USER>\";\n\nexport default function Home() {\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    const lenis = new Lenis();\n\n    function raf(time: number) {\n      lenis.raf(time);\n      requestAnimationFrame(raf);\n    }\n\n    setTimeout(() => {\n      setIsLoading(false);\n      document.body.style.cursor = \"default\";\n      window.scrollTo(0, 0);\n    }, 2000);\n\n    requestAnimationFrame(raf);\n  }, []);\n  return (\n    <>\n      <AnimatePresence mode=\"wait\">\n        {isLoading && <Preloader />}\n      </AnimatePresence>\n      <HomeHero />\n      <AboutHome />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,gVAAQ,EAAC;IAE3C,IAAA,iVAAS,EAAC;QACR,MAAM,QAAQ,IAAI,0NAAK;QAEvB,SAAS,IAAI,IAAY;YACvB,MAAM,GAAG,CAAC;YACV,sBAAsB;QACxB;QAEA,WAAW;YACT,aAAa;YACb,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;YAC7B,OAAO,QAAQ,CAAC,GAAG;QACrB,GAAG;QAEH,sBAAsB;IACxB,GAAG,EAAE;IACL,qBACE;;0BACE,6WAAC,kTAAe;gBAAC,MAAK;0BACnB,2BAAa,6WAAC,sJAAS;;;;;;;;;;0BAE1B,6WAAC,6IAAQ;;;;;0BACT,6WAAC,qIAAS;;;;;;;AAGhB", "debugId": null}}]}